import React from 'react';
import PinLoginPage from './pages/PinLoginPage';
import { useAuthStore } from './stores/authStore';
import './App.css';

function App() {
  const { isAuthenticated } = useAuthStore();

  // Eğer kullanıcı giriş yapmışsa ana uygulamayı göster
  // Şimdilik sadece PIN giriş sayfasını gösteriyoruz
  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-green-600 mb-4">
            Vardiya Başarıyla Başlatıldı! 🎉
          </h1>
          <p className="text-gray-600">
            Ana POS uygulaması burada gösterilecek.
          </p>
        </div>
      </div>
    );
  }

  return <PinLoginPage />;
}

export default App;

