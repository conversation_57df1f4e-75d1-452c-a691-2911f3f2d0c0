import React, { useState, useEffect } from 'react';
import { Layout, Typography, Card, Button, Space, Alert } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import './App.css';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

interface HealthStatus {
  status: string;
  message: string;
  timestamp: string;
  database: string;
}

function App() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkHealth = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('http://localhost:3001/api/health');
      const data = await response.json();
      setHealthStatus(data);
    } catch (err) {
      setError('Backend bağlantısı kurulamadı. Backend sunucusunun çalıştığından emin olun.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkHealth();
  }, []);

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#001529', padding: '0 24px' }}>
        <Title level={2} style={{ color: 'white', margin: '16px 0' }}>
          Atropos POS Sistemi
        </Title>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Card title="Sistem Durumu" style={{ maxWidth: 600 }}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                onClick={checkHealth} 
                loading={loading}
                style={{ marginBottom: 16 }}
              >
                Sistem Durumunu Kontrol Et
              </Button>
              
              {error && (
                <Alert
                  message="Bağlantı Hatası"
                  description={error}
                  type="error"
                  icon={<ExclamationCircleOutlined />}
                  showIcon
                />
              )}
              
              {healthStatus && !error && (
                <Alert
                  message="Sistem Çalışıyor"
                  description={
                    <div>
                      <Text strong>Durum:</Text> {healthStatus.status}<br />
                      <Text strong>Mesaj:</Text> {healthStatus.message}<br />
                      <Text strong>Veritabanı:</Text> {healthStatus.database}<br />
                      <Text strong>Zaman:</Text> {new Date(healthStatus.timestamp).toLocaleString('tr-TR')}
                    </div>
                  }
                  type="success"
                  icon={<CheckCircleOutlined />}
                  showIcon
                />
              )}
            </Space>
          </Card>
          
          <Card title="Hoş Geldiniz" style={{ maxWidth: 600 }}>
            <Text>
              Atropos POS sistemi başarıyla kuruldu. Bu minimal başlangıç projesi 
              aşağıdaki teknolojileri içermektedir:
            </Text>
            <ul style={{ marginTop: 16 }}>
              <li>Frontend: Electron + React + TypeScript + Ant Design</li>
              <li>Backend: Express + TypeScript + Prisma + PostgreSQL</li>
            </ul>
          </Card>
        </Space>
      </Content>
    </Layout>
  );
}

export default App;

