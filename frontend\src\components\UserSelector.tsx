import React from 'react';
import { Card, Avatar, Typography, Select } from 'antd';
import { UserOutlined, DownOutlined } from '@ant-design/icons';
import { User } from '../stores/authStore';

const { Text } = Typography;
const { Option } = Select;

interface UserSelectorProps {
  users: User[];
  selectedUser: User | null;
  onUserSelect: (user: User) => void;
  loading?: boolean;
}

const UserSelector: React.FC<UserSelectorProps> = ({
  users,
  selectedUser,
  onUserSelect,
  loading = false,
}) => {
  return (
    <div className="w-full max-w-md mx-auto">
      <Text className="block mb-4 text-gray-600 text-center">
        Vardiya başlatmak için hesabınızı seçin.
      </Text>
      
      <Select
        size="large"
        placeholder="Hesap seçin"
        value={selectedUser?.id}
        onChange={(userId) => {
          const user = users.find(u => u.id === userId);
          if (user) onUserSelect(user);
        }}
        loading={loading}
        className="w-full"
        suffixIcon={<DownOutlined />}
        dropdownStyle={{ padding: '8px 0' }}
      >
        {users.map((user) => (
          <Option key={user.id} value={user.id}>
            <div className="flex items-center gap-3 py-2">
              <Avatar 
                size={40}
                src={user.avatar}
                icon={<UserOutlined />}
                className="flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 truncate">
                  {user.fullName}
                </div>
                <div className="text-sm text-gray-500 truncate">
                  {user.shiftStart && user.shiftEnd
                    ? `${user.shiftStart} - ${user.shiftEnd}`
                    : 'Vardiya saati belirtilmemiş'
                  }
                </div>
              </div>
            </div>
          </Option>
        ))}
      </Select>
      
      {selectedUser && (
        <Card className="mt-6 bg-blue-50 border-blue-200">
          <div className="flex items-center gap-4">
            <Avatar 
              size={48}
              src={selectedUser.avatar}
              icon={<UserOutlined />}
            />
            <div className="flex-1">
              <div className="font-semibold text-gray-900">
                {selectedUser.fullName}
              </div>
              <div className="text-sm text-gray-600">
                {selectedUser.shiftStart && selectedUser.shiftEnd 
                  ? `${selectedUser.shiftStart} - ${selectedUser.shiftEnd}`
                  : 'Vardiya saati belirtilmemiş'
                }
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default UserSelector;
