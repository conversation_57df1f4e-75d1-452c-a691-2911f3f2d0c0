import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import { AuthService } from '../services/auth.service';
import { JwtPayload } from '../types/auth.types';

// Request interface'ini genişlet
declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
      sessionId?: string;
    }
  }
}

export class AuthMiddleware {
  private authService: AuthService;

  constructor(authService: AuthService) {
    this.authService = authService;
  }

  /**
   * JWT token doğrulama middleware'i
   */
  verifyToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 1. Authorization header'ını kontrol et
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          message: 'Token bulunamadı',
          code: 'TOKEN_MISSING'
        });
        return;
      }

      // 2. Token'ı çıkar
      const token = authHeader.substring(7); // "Bearer " kısmını çıkar

      // 3. Token'ı doğrula
      const payload = await this.authService.verifyToken(token);
      
      if (!payload) {
        res.status(401).json({
          success: false,
          message: 'Geçersiz token',
          code: 'INVALID_TOKEN'
        });
        return;
      }

      // 4. Request'e user bilgilerini ekle
      req.user = payload;
      req.sessionId = payload.sessionId;

      next();

    } catch (error) {
      console.error('JWT verification error:', error);
      res.status(401).json({
        success: false,
        message: 'Token doğrulama hatası',
        code: 'TOKEN_VERIFICATION_ERROR'
      });
    }
  };

  /**
   * Opsiyonel token doğrulama (token varsa doğrula, yoksa devam et)
   */
  optionalVerifyToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const payload = await this.authService.verifyToken(token);
        
        if (payload) {
          req.user = payload;
          req.sessionId = payload.sessionId;
        }
      }

      next();

    } catch (error) {
      // Opsiyonel doğrulamada hata olursa sadece log'la, devam et
      console.error('Optional JWT verification error:', error);
      next();
    }
  };

  /**
   * Role tabanlı yetkilendirme
   */
  requireRole = (allowedRoles: string[]) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        const user = req.user;
        
        if (!user) {
          res.status(401).json({
            success: false,
            message: 'Kimlik doğrulama gerekli',
            code: 'AUTHENTICATION_REQUIRED'
          });
          return;
        }

        if (!allowedRoles.includes(user.role)) {
          res.status(403).json({
            success: false,
            message: 'Bu işlem için yetkiniz yok',
            code: 'INSUFFICIENT_PERMISSIONS'
          });
          return;
        }

        next();

      } catch (error) {
        console.error('Role verification error:', error);
        res.status(500).json({
          success: false,
          message: 'Yetki kontrolü hatası',
          code: 'AUTHORIZATION_ERROR'
        });
      }
    };
  };

  /**
   * Branch tabanlı yetkilendirme
   */
  requireBranch = (req: Request, res: Response, next: NextFunction): void => {
    try {
      const user = req.user;
      const requestedBranchId = req.params.branchId || req.body.branchId;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Kimlik doğrulama gerekli',
          code: 'AUTHENTICATION_REQUIRED'
        });
        return;
      }

      // Super admin ve admin'ler tüm şubelere erişebilir
      if (['SUPER_ADMIN', 'ADMIN'].includes(user.role)) {
        next();
        return;
      }

      // Diğer kullanıcılar sadece kendi şubelerine erişebilir
      if (user.branchId && user.branchId !== requestedBranchId) {
        res.status(403).json({
          success: false,
          message: 'Bu şubeye erişim yetkiniz yok',
          code: 'BRANCH_ACCESS_DENIED'
        });
        return;
      }

      next();

    } catch (error) {
      console.error('Branch verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Şube yetki kontrolü hatası',
        code: 'BRANCH_AUTHORIZATION_ERROR'
      });
    }
  };
}

/**
 * PIN giriş rate limiting
 */
export const pinLoginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 dakika
  max: 5, // 15 dakikada maksimum 5 deneme
  message: {
    success: false,
    message: 'Çok fazla giriş denemesi. 15 dakika sonra tekrar deneyin.',
    code: 'TOO_MANY_ATTEMPTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // IP bazlı rate limiting
  keyGenerator: (req: Request) => {
    return req.ip || req.connection.remoteAddress || 'unknown';
  }
});

/**
 * Genel API rate limiting
 */
export const generalRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 dakika
  max: 100, // 1 dakikada maksimum 100 istek
  message: {
    success: false,
    message: 'Çok fazla istek. Lütfen daha sonra tekrar deneyin.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false
});
