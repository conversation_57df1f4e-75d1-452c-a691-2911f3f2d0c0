---
group: Design Patterns
type: Principles
order: 8
title: Provide an Invitation
---

A common problem with many of these rich interactions (e.g. Drag and Drop, Inline Editing, and Contextual Tools) is their lack of discoverability. Providing an invitation to the user is one of the keys to successful interactive interfaces.

Invitations are the prompts and cues that lead users through an interaction. They often include just-in-time tips or visual affordances that hint at what will happen next in the interface.

> **Signifiers** are signals, communication devices. These signs tell you about the possible actions; what to do, and where to do it. Signifiers are often visible, audible or tangible, from the Design of Everyday Things.

> **Affordances** are the relationships (read: possible actions) between an object and an entity (most often a person). The presence of an affordance is determined by the properties of the object and of the abilities of the entity who's interacting with the object, from the Design of Everyday Things.

---

## Static Invitations

By providing cues for interaction directly on the page we can statically indicate to the user the expected interface behavior. Static Invitations provide cues directly on the page.

<br>

<ImagePreview>
<img class="preview-img" alt="example of Text Invitation" src="https://gw.alipayobjects.com/zos/rmsportal/ZeMSbCHmvWETbssJHRvo.png">
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="example of Blank Slate Invitation" src="https://gw.alipayobjects.com/zos/rmsportal/PHxVAFKncyXDCFUJInbB.png">
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="example of Unfinished Invitation" src="https://gw.alipayobjects.com/zos/rmsportal/ChvxJAQTwWbqzBnUBLec.png">
</ImagePreview>

Call to Action Invitations are generally provided as static instructions on the page. But visually they can be provided in many different ways such as Text Invitation, Blank Slate Invitation and Unfinished Invitation.

<br>

<ImagePreview>
<img class="preview-img" alt="example 1 of Tour Invitation" description="A few of tour points are provided when the user first logs in. Clicking the 'Got It' button leads the user to the next tour step." src="https://gw.alipayobjects.com/zos/rmsportal/dMrVeJJiaCLzoYfJrJKe.png">
</ImagePreview>

Tour invitation can be a nice way to explain design changes to a web application, especially for a well-designed interface. But providing tours will not solve the real problems an interface may have during interaction.

> Note that make Tour Invitations short and simple, easy to exit, and clear to restart.

<br>

---

## Dynamic Invitations

Dynamic Invitations engage users at the point of the interaction and guide them through the next step of interaction.

<br>

<ImagePreview>
<img class="preview-img" alt="example 1 of Hover Invitation" description="During mouse hover on the whole card, the clickable parts turn to blue hypertext." src="https://gw.alipayobjects.com/zos/rmsportal/ejvYAogJXLPqoMUqyvIV.png">
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="example 2 of Hover Invitation" description="During mouse hover, the button of &quot;Select this Template&quot; appears." src="https://gw.alipayobjects.com/zos/rmsportal/umGVwLlIJSmxaQXcjlbh.png">
</ImagePreview>

Hover Invitation: Provide an invitation during mouse hover.

<br>

<ImagePreview>
<img class="preview-img" alt="example of Inference Invitation" description="The system predicts that the user's interest in an article extends to a type of articles, and it provides an invitation after the user click &quot;like&quot;." src="https://gw.alipayobjects.com/zos/rmsportal/iuLdCuNQWCvYuTxxQUuL.png">
</ImagePreview>

Inference Invitation: Use visual inferences during interaction to cue users as to what the system has inferred about their intent.

<br>

<ImagePreview>
<img class="preview-img" alt="example of More Content Invitation" description="Use the left or right arrows to switch more content around Modal." src="https://os.alipayobjects.com/rmsportal/sOqYOydwQjLHqph.png">
</ImagePreview>

More Content Invitation: Indicate that there is more content on the page.

<br>
