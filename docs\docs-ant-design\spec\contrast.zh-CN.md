---
group: 设计模式
type: 原则
order: 3
title: 对比
---

对比是增加视觉效果最有效方法之一，同时也能在不同元素之间建立一种有组织的层次结构，让用户快速识别关键信息。

> 注：要实现有效的对比，对比就必须强烈，切不可畏畏缩缩。

---

## 主次关系对比

<ImagePreview>
<img class="preview-img good" alt="正确示例" src="https://gw.alipayobjects.com/zos/rmsportal/DXDSNzVmrVwVRJCTyaTH.png">
<img class="preview-img bad" alt="错误示例" src="https://gw.alipayobjects.com/zos/rmsportal/tMlELOuJrJrrYtTAbnlu.png">
</ImagePreview>

为了让用户能在操作上（类似表单、弹出框等场景）快速做出判断， 来突出其中一项相对更重要或者更高频的操作。

> 注意：突出的方法，不局限于强化重点项，也可以是弱化其他项。

<br>

<ImagePreview>
<img class="preview-img" alt="不区分主次的示例" description="「通过」和「驳回」都使用次按钮，系统保持中立。" src="https://gw.alipayobjects.com/zos/rmsportal/gniiMTPEHagxaelGBjAe.png">
</ImagePreview>

在一些需要用户慎重决策的场景中，系统应该保持中立，不能替用户或者诱导用户做出判断。

---

## 总分关系对比

<ImagePreview>
<img class="preview-img" alt="总分关系示例 1" src="https://gw.alipayobjects.com/zos/rmsportal/mGCufzQKHZvViwxAVPPY.png">
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="总分关系示例 2" src="https://gw.alipayobjects.com/zos/rmsportal/vQrVvLzKbGXbZotcaMVg.png">
</ImagePreview>

通过调整排版、字体、大小等方式来突出层次感，区分总分关系，使得页面更具张力和节奏感。

---

## 状态关系对比

<ImagePreview>
<img class="preview-img" alt="静态对比示例" description="用不同颜色点，来表明不同状态。" src="https://gw.alipayobjects.com/zos/rmsportal/PMVYKxaLBApJFyXAxkHy.png">
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="动态对比示例" description="鼠标悬停时，该项和其他项呈现出明显不同的视觉效果，响应用户操作。" src="https://gw.alipayobjects.com/zos/rmsportal/WXNjOhgQDMnNoieFrFMP.png">
</ImagePreview>

通过改变颜色、增加辅助形状等方法来实现状态关系的对比，以便用户更好的区分信息。

常见类型有「静态对比」、「动态对比」。
