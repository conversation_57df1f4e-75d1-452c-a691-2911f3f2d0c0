---
group: Design Patterns
type: Principles
order: 3
title: Contrast
---

Contrast is one of the effective ways to add visual interest to your page, and to create an organizational hierarchy among different elements that aid user in finding the information quickly.

> Note: The important rule for contrast to be effective, it must be strong. Don't be wimp.

---

## The Contrast of major and minor relationship

<ImagePreview>
<img class="preview-img good" alt="good example" src="https://gw.alipayobjects.com/zos/rmsportal/DXDSNzVmrVwVRJCTyaTH.png">
<img class="preview-img bad" alt="bad example" src="https://gw.alipayobjects.com/zos/rmsportal/tMlELOuJrJrrYtTAbnlu.png">
</ImagePreview>

In order to help user make a quick operation (something like the form, modal), a more important operation or an operation with higher frequency would be emphasized.

> Notes: ways of emphasizing are not just to intensify the key item. It could also weaken the other items.

<br>

<ImagePreview>
<img class="preview-img" alt="Example of ignoring the primary and secondary sequence" description="Accept and Reject should use default button, for UI should not affect user's decision." src="https://gw.alipayobjects.com/zos/rmsportal/gniiMTPEHagxaelGBjAe.png">
</ImagePreview>

When there's something needs users to make decision prudently, the system should remain neutral. It shouldn't make the decision for users or lead them to make judgement.

---

## Contrast of whole and part

<ImagePreview>
<img class="preview-img" alt="Example of whole and part 1" src="https://gw.alipayobjects.com/zos/rmsportal/mGCufzQKHZvViwxAVPPY.png">
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="Example of whole and part 2" src="https://gw.alipayobjects.com/zos/rmsportal/vQrVvLzKbGXbZotcaMVg.png">
</ImagePreview>

Taking advantage of changing the typesetting, the typeface and the size, we highlight the different levels and differentiate the ensemble and the part, which would make the page be more flexible and rhythmic.

---

## Contrast of the state relation

<ImagePreview>
<img class="preview-img" alt="Example of static contrast" description="Points with various colors would be used to show different states. " src="https://gw.alipayobjects.com/zos/rmsportal/PMVYKxaLBApJFyXAxkHy.png">
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="Example of dynamic contrast" description="When the mouse doesn't be moved, this item and other items would show different visual effects obviously, which would influence the user's operation." src="https://gw.alipayobjects.com/zos/rmsportal/WXNjOhgQDMnNoieFrFMP.png">
</ImagePreview>

Taking advantage of changing colors and adding assistant shapes, we realize the comparison of state relation, which could help users differentiate various information better

The forms we usually see include 「static contrast」 and 「dynamic contrast」.
