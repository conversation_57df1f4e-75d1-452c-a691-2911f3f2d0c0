import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { 
  PinLoginRequest, 
  SessionData, 
  JwtPayload, 
  AuthErrorCodes,
  PinLoginResponse,
  AuthError 
} from '../types/auth.types';

export class AuthService {
  private prisma: PrismaClient;
  private jwtSecret: string;
  private jwtExpiresIn: string;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '8h';
  }

  /**
   * PIN ile kullanıcı girişi
   */
  async pinLogin(request: PinLoginRequest): Promise<PinLoginResponse | AuthError> {
    try {
      // 1. Kullanıcıyı bul
      const user = await this.findUserByUsername(request.username);
      if (!user) {
        return this.createError(AuthErrorCodes.USER_NOT_FOUND, 'Kullanıcı bulunamadı');
      }

      // 2. Kullanıcı aktif mi kontrol et
      if (!user.active) {
        return this.createError(AuthErrorCodes.USER_INACTIVE, 'Kullanıcı hesabı deaktif');
      }

      // 3. PIN kontrolü
      if (!user.pin) {
        return this.createError(AuthErrorCodes.INVALID_PIN, 'Kullanıcı için PIN tanımlanmamış');
      }

      const isPinValid = await this.validatePin(request.pin, user.pin);
      if (!isPinValid) {
        return this.createError(AuthErrorCodes.INVALID_CREDENTIALS, 'Geçersiz PIN');
      }

      // 4. Session oluştur
      const sessionData: SessionData = {
        userId: user.id,
        branchId: user.branchId || 'default-branch', // Eğer null ise default branch
        deviceInfo: request.deviceInfo
      };

      const session = await this.createSession(sessionData);
      if (!session) {
        return this.createError(AuthErrorCodes.SESSION_CREATION_FAILED, 'Oturum oluşturulamadı');
      }

      // 5. JWT token oluştur
      const token = await this.generateJwtToken({
        userId: user.id,
        username: user.username,
        role: user.role,
        branchId: user.branchId,
        sessionId: session.id
      });

      if (!token) {
        return this.createError(AuthErrorCodes.TOKEN_GENERATION_FAILED, 'Token oluşturulamadı');
      }

      // 6. Session'ı gerçek JWT token ile güncelle
      await this.prisma.session.update({
        where: { id: session.id },
        data: { token }
      });

      // 7. Son giriş zamanını güncelle
      await this.updateLastLogin(user.id);

      // 8. Başarılı response
      return {
        success: true,
        message: 'Giriş başarılı',
        data: {
          token,
          user: {
            id: user.id,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            branchId: user.branchId
          },
          session: {
            id: session.id,
            startedAt: session.startedAt.toISOString()
          }
        }
      };

    } catch (error) {
      console.error('PIN login error:', error);
      return this.createError(AuthErrorCodes.INVALID_CREDENTIALS, 'Giriş işlemi başarısız');
    }
  }

  /**
   * PIN hash'leme
   */
  async hashPin(pin: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(pin, saltRounds);
  }

  /**
   * PIN doğrulama
   */
  private async validatePin(plainPin: string, hashedPin: string): Promise<boolean> {
    return bcrypt.compare(plainPin, hashedPin);
  }

  /**
   * Kullanıcı arama
   */
  private async findUserByUsername(username: string) {
    return this.prisma.user.findUnique({
      where: { username },
      select: {
        id: true,
        username: true,
        pin: true,
        firstName: true,
        lastName: true,
        role: true,
        branchId: true,
        active: true
      }
    });
  }

  /**
   * Session oluşturma
   */
  private async createSession(sessionData: SessionData) {
    // Geçici unique token oluştur
    const tempToken = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return this.prisma.session.create({
      data: {
        userId: sessionData.userId,
        branchId: sessionData.branchId,
        deviceInfo: sessionData.deviceInfo,
        token: tempToken, // Geçici token, sonra JWT ile güncelleyeceğiz
        lastActivityAt: new Date()
      }
    });
  }

  /**
   * JWT token oluşturma
   */
  private async generateJwtToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): Promise<string | null> {
    try {
      const tokenPayload = {
        userId: payload.userId,
        username: payload.username,
        role: payload.role,
        branchId: payload.branchId,
        sessionId: payload.sessionId
      };

      return (jwt as any).sign(tokenPayload, this.jwtSecret, { expiresIn: this.jwtExpiresIn });
    } catch (error) {
      console.error('JWT generation error:', error);
      return null;
    }
  }

  /**
   * Son giriş zamanını güncelleme
   */
  private async updateLastLogin(userId: string) {
    await this.prisma.user.update({
      where: { id: userId },
      data: { lastLoginAt: new Date() }
    });
  }

  /**
   * Hata response oluşturma
   */
  private createError(code: AuthErrorCodes, message: string): AuthError {
    return {
      success: false,
      message,
      code
    };
  }

  /**
   * Session sonlandırma
   */
  async endSession(sessionId: string): Promise<boolean> {
    try {
      await this.prisma.session.update({
        where: { id: sessionId },
        data: { endedAt: new Date() }
      });
      return true;
    } catch (error) {
      console.error('End session error:', error);
      return false;
    }
  }

  /**
   * JWT token doğrulama
   */
  async verifyToken(token: string): Promise<JwtPayload | null> {
    try {
      return (jwt as any).verify(token, this.jwtSecret) as JwtPayload;
    } catch (error) {
      return null;
    }
  }

  /**
   * Aktif kullanıcıları getir (PIN giriş için)
   */
  async getActiveUsers() {
    try {
      const users = await this.prisma.user.findMany({
        where: {
          active: true,
          pin: { not: null } // PIN'i olan kullanıcılar
        },
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          role: true,
          email: true,
          phone: true
        },
        orderBy: {
          firstName: 'asc'
        }
      });

      // Frontend için format düzenle
      return users.map(user => ({
        id: user.id,
        username: user.username,
        fullName: `${user.firstName} ${user.lastName}`,
        role: user.role,
        avatar: null, // Şimdilik avatar yok
        shiftStart: '08:00', // Mock data - gerçekte shift tablosundan gelecek
        shiftEnd: '16:00'
      }));

    } catch (error) {
      console.error('Get active users error:', error);
      throw new Error('Kullanıcılar yüklenirken hata oluştu');
    }
  }
}
