# Atropos POS Sistemi

Minimal restoran POS sistemi başlangıç projesi.

## Teknoloji Stack

### Frontend
- **Electron**: Desktop uygulama framework'ü
- **React**: UI kütüphanesi
- **TypeScript**: Tip güvenli JavaScript
- **Ant Design**: UI component kütüphanesi
- **Vite**: Build tool

### Backend
- **Express**: Node.js web framework'ü
- **TypeScript**: Tip güvenli JavaScript
- **Prisma**: ORM (Object-Relational Mapping)
- **PostgreSQL**: Veritabanı

## Proje <PERSON>

```
atropos/
├── frontend/          # Electron + React uygulaması
│   ├── public/
│   │   └── electron.js    # Electron ana dosyası
│   ├── src/
│   │   ├── App.tsx        # Ana React component
│   │   └── main.tsx       # React entry point
│   ├── package.json
│   └── tsconfig.json
├── backend/           # Express API sunucusu
│   ├── src/
│   │   ├── app.ts         # Express uygulaması
│   │   └── routes/
│   │       └── health.ts  # Health check endpoint
│   ├── prisma/
│   │   └── schema.prisma  # Veritabanı şeması
│   ├── package.json
│   └── tsconfig.json
└── README.md
```

## Kurulum ve Çalıştırma

### Backend

1. Backend dizinine gidin:
   ```bash
   cd backend
   ```

2. Bağımlılıkları yükleyin:
   ```bash
   npm install
   ```

3. Prisma client'ı generate edin:
   ```bash
   npx prisma generate  
   ```
cd backend ; npx prisma migrate dev --name init
   ```

4. Sunucuyu başlatın:
   ```bash
   npm run dev
   ```

Backend http://localhost:3001 adresinde çalışacaktır.

### Frontend

1. Frontend dizinine gidin:
   ```bash
   cd frontend
   ```

2. Bağımlılıkları yükleyin:
   ```bash
   npm install
   ```

3. React development server'ı başlatın:
   ```bash
   npm run dev
   ```

4. Electron uygulamasını başlatın (opsiyonel):
   ```bash
   npm run electron-dev
   ```

Frontend http://localhost:5173 adresinde çalışacaktır.

## API Endpoints

### Health Check
- **GET** `/api/health` - Sistem durumu kontrolü

Örnek response:
```json
{
  "status": "OK",
  "message": "Atropos POS Backend is running",
  "timestamp": "2025-07-11T22:30:00.000Z",
  "database": "Connected"
}
```

## Veritabanı Şeması

Proje temel POS sistemi için gerekli modelleri içerir:

- **User**: Kullanıcı bilgileri
- **Category**: Ürün kategorileri
- **Product**: Ürün bilgileri

## Geliştirme Notları

- Backend TypeScript strict mode ile yapılandırılmıştır
- Frontend Ant Design component'leri kullanır
- CORS tüm origin'ler için etkinleştirilmiştir
- Health check endpoint veritabanı bağlantısını test eder
- Electron uygulaması development mode'da DevTools açar

## Sonraki Adımlar

1. PostgreSQL veritabanı kurulumu
2. Prisma migration'ları
3. Kullanıcı authentication
4. Ürün yönetimi sayfaları
5. Satış işlemleri
6. Raporlama modülü

