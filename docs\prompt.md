## 🔧 BACKEND PROMPT
```markdown
## Backend: [FEATURE ADI] API

### İhtiyaç
[Ne için kullanılacak - örn: PIN ile kullanıcı girişi]

### Özellikler
- [Özellik 1]
- [Özellik 2]

### Teknik Gereksinimler
- Express + TypeScript
- Prisma ORM (schema hazır)
- Zod validation
- JWT authentication
- Proper error handling (try-catch + middleware)
- HTTP status codes kullan

### Notlar
- [Varsa özel durum]
```

## 🎨 FRONTEND PROMPT
```markdown
## Frontend: [FEATURE ADI] Sayfası

### Tasarım
[Screenshot varsa ekle]

### Özellikler
- [UI özellik 1]
- [UI özellik 2]

### Backend API
[Hangi endpoint'ler var - örn: /api/auth/pin-login]

### Teknik Gereksinimler
- React + TypeScript
- Ant Design componentleri (Button, Table, Form, Modal, Spin, message)
- Ant Design Form (built-in validation kullan, React Hook Form'a gerek yok)
- Zustand state management
- Axios + interceptors
- Loading states (Spin component)
- Error handling (message.error(), notification)

### Notlar
- Türkçe UI
- Ant Design varsayılan teması kullanılacak
- [Varsa özel durum]
```

