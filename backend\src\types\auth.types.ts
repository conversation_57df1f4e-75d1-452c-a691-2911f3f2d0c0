import { z } from 'zod';

// ==================== REQUEST SCHEMAS ====================

export const PinLoginSchema = z.object({
  username: z.string()
    .min(1, '<PERSON>llanıcı adı gereklidir')
    .max(50, 'Kullanıcı adı çok uzun'),
  pin: z.string()
    .min(4, 'PIN en az 4 haneli olmalıdır')
    .max(6, 'PIN en fazla 6 haneli olmalıdır')
    .regex(/^\d+$/, 'PIN sadece rakam içermelidir'),
  deviceInfo: z.string().optional()
});

// ==================== RESPONSE TYPES ====================

export interface PinLoginResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: {
      id: string;
      username: string;
      firstName: string;
      lastName: string;
      role: string;
      branchId: string | null;
    };
    session: {
      id: string;
      startedAt: string;
    };
  };
}

export interface AuthError {
  success: false;
  message: string;
  code: string;
}

// ==================== JWT PAYLOAD ====================

export interface JwtPayload {
  userId: string;
  username: string;
  role: string;
  branchId: string | null;
  sessionId: string;
  iat?: number;
  exp?: number;
}

// ==================== SERVICE TYPES ====================

export interface PinLoginRequest {
  username: string;
  pin: string;
  deviceInfo?: string;
}

export interface SessionData {
  userId: string;
  branchId: string;
  deviceInfo?: string;
}

// ==================== ERROR CODES ====================

export enum AuthErrorCodes {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  USER_INACTIVE = 'USER_INACTIVE',
  INVALID_PIN = 'INVALID_PIN',
  TOO_MANY_ATTEMPTS = 'TOO_MANY_ATTEMPTS',
  SESSION_CREATION_FAILED = 'SESSION_CREATION_FAILED',
  TOKEN_GENERATION_FAILED = 'TOKEN_GENERATION_FAILED'
}
