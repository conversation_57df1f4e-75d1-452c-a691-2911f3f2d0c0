---
group: Ant Design
order: 1
title: Design Values
---

The design values of Ant Design provide designers with internal standards for evaluation, enlighten and inspire the design principles and design patterns, and then offer guidance and general solutions for specific design problems.

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*sGufTKB_F5QAAAAAAAAAAABkARQnAQ" alt="General" />
</div>

Here are four design values of Ant Design:

## Natural

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*zx7LTI_ECSAAAAAAAAAAAABkARQnAQ" alt="Natural" />
</div>

The light-speed iteration of the digital world makes products more complex. However, human consciousness and attention resources are limited. Facing this design contradiction, the pursuit of natural interaction will be the consistent direction of Ant Design.

- **Natural user cognition**: According to cognitive psychology, about 80% of external information is obtained through visual channels. The most important visual elements in the interface design, including layout, colors, illustrations, icons, etc., should fully absorb the laws of nature, thereby reducing the user's cognitive cost and bringing authentic and smooth feelings. In some scenarios, opportunely adding other sensory channels such as hearing, touch can create a richer and more natural product experience.
- **Natural user behavior**: In the interaction with the system, the designer should fully understand the relationship between users, system roles, and task objectives, and also contextually organize system functions and services. At the same time, a series of methods such as behavior analysis, artificial intelligence and sensors could be applied to assist users to make effective decisions and reduce extra operations of users, to save users' mental and physical resources and make human-computer interaction more natural.

> To get to know the past and present of natural values, [please move to the column](https://zhuanlan.zhihu.com/p/44809866).

## Certain

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*OCU3RKZrw8QAAAAAAAAAAABkARQnAQ" alt="Certain" />
</div>

Interfaces are the medium of interaction between users and the system. They are the means rather than the purpose. Based on the pursuit of "natural" interaction, the product interfaces created by Ant Design should be high certainty and low cooperative entropy.

- **Designer certainty**: Enterprise products are made by collaboration. The more participants, the higher the entropy of cooperation. This is why low-efficiency design and difficult maintenance of the product system exists. By exploring the design rules and modular design ideas, designers should be provided with simplified design rules, components and patterns so they can reduce the cooperative entropy and a more efficient design process.
  - **Keep restraint**: Don't make a decision before you figure it out. Designers should focus on the most valuable product features using minimal design elements to express. As Antoine de St. Exupery said: "Perfection is achieved, not when there is nothing more to add, but when there is nothing left to take away."
  - **Object-oriented**: Explore design rules and abstract them as "objects" to enhance the flexibility and maintainability of user interface design, while reducing the designer's subjective judgment and uncertainty of the system. For example, color value conversion and spacing typesetting.
  - **Modular design**: Encapsulating the complex or reusable parts could provide limited interfaces to interact with other modules, ultimately reducing overall system complexity, thereby improving reliability and maintainability. Designers can use existing resources or abstract their reusable resources to save the unnecessary and low additional design to keep their focus on where creativity is most needed.
- **User certainty**: User's daily work is completed through the collaboration of enterprise products. In addition to considering the consistency of a single product design, good certainty is required to be maintained across products, terminals, and systems. Consistent appearance and interaction, maintaining a familiarity to user, can reduce the difficulty of learning, cognitive and operating costs, and improve work efficiency.

## Meaningful

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*xOYlR4e8ihIAAAAAAAAAAABkARQnAQ" alt="Meaningful" />
</div>

A product or function is created by a designer not because of the designer's needs, but to carry a user's work mission. Product design should be user-centered to promote the achievement of the user's mission. Simultaneously, based on "nature" and "certainty" design values, we should regard user's human needs and create meaningful human-computer interaction for the work process.

- **Meaning of result**: Clear goals, immediate feedback. Understand the objectives, clearly disassemble the sub-objectives according to the use process, and let each interaction revolve around the achievement of the main objectives. Provide appropriate and immediate feedback for each action, so that users can understand the operation results. Besides, emotional design can be used to pacify users' negative emotions and enhance users' positive emotions.
- **Meaning of process**: Moderate challenge, full devotion. Adjust the difficulty of work in different scenarios, make the function trigger at the right time to match the user's skill. If not necessary, do not add entities. Do not distract users, let users focus on task achievement, rather than the interface. Let the current work be neither to simple nor too complex. The challenges are moderate, but higher challenges are raised as the user's capabilities grow. It allows users to continue to immerse themselves in the flow of work and gain fulfilling work experience.

## Growing

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*pKz3TabovrEAAAAAAAAAAABkARQnAQ" alt="Growing" />
</div>

The growth of enterprise product's capabilities is accompanied by the evolution of user system roles. Designers should be responsible for the products they create and improve the discoverability of functions and values. Designers should design with the vision of development and consider the common growth of both ends of humans and computers.

- **Value connection**: The growth of products depends on the expansion and deep use of users, while the growth of users depends on the growth of product functions. Designers should establish system design thinking, understand the value of product functions, explore user needs in different scenarios, and establish a connection between value and needs. Let product value be discovered and help users build more effective and efficient ways of working.
- **Man-Computer Symbiosis**: More connections between product functions and user requirements make human-computer interaction closer and users and system are growing together. When designing products, users and systems should not be separated from each other. They should be considered as a dynamic group to ensure that they are flexible, inclusive and full of vitality.
