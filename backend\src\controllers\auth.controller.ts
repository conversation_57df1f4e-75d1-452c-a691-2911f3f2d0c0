import { Request, Response } from 'express';
import { AuthService } from '../services/auth.service';
import { PinLoginSchema, AuthErrorCodes } from '../types/auth.types';
import { ZodError } from 'zod';

export class AuthController {
  private authService: AuthService;

  constructor(authService: AuthService) {
    this.authService = authService;
  }

  /**
   * PIN ile giriş endpoint'i
   */
  pinLogin = async (req: Request, res: Response): Promise<void> => {
    try {
      // 1. Request validation
      const validationResult = PinLoginSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        res.status(400).json({
          success: false,
          message: 'Geçersiz istek verisi',
          errors: this.formatValidationErrors(validationResult.error)
        });
        return;
      }

      // 2. Device info'yu request'ten al (IP, User-Agent vs.)
      const deviceInfo = this.extractDeviceInfo(req);
      const loginRequest = {
        ...validationResult.data,
        deviceInfo: validationResult.data.deviceInfo || deviceInfo
      };

      // 3. Auth service'i çağır
      const result = await this.authService.pinLogin(loginRequest);

      // 4. Response'u döndür
      if (result.success) {
        res.status(200).json(result);
      } else {
        // Hata koduna göre HTTP status belirle
        const statusCode = this.getStatusCodeForError((result as any).code);
        res.status(statusCode).json(result);
      }

    } catch (error) {
      console.error('PIN login controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Sunucu hatası',
        code: 'INTERNAL_SERVER_ERROR'
      });
    }
  };

  /**
   * Aktif kullanıcı listesi endpoint'i
   */
  getUsers = async (req: Request, res: Response): Promise<void> => {
    try {
      const users = await this.authService.getActiveUsers();

      res.status(200).json({
        success: true,
        data: users
      });

    } catch (error) {
      console.error('Get users controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Kullanıcılar yüklenirken hata oluştu',
        code: 'INTERNAL_SERVER_ERROR'
      });
    }
  };

  /**
   * Çıkış endpoint'i
   */
  logout = async (req: Request, res: Response): Promise<void> => {
    try {
      // JWT middleware'den gelen session bilgisi
      const sessionId = (req as any).sessionId;
      
      if (sessionId) {
        await this.authService.endSession(sessionId);
      }

      res.status(200).json({
        success: true,
        message: 'Çıkış başarılı'
      });

    } catch (error) {
      console.error('Logout controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Çıkış işlemi başarısız',
        code: 'LOGOUT_FAILED'
      });
    }
  };

  /**
   * Token doğrulama endpoint'i
   */
  verifyToken = async (req: Request, res: Response): Promise<void> => {
    try {
      // JWT middleware'den gelen user bilgisi
      const user = (req as any).user;
      
      res.status(200).json({
        success: true,
        message: 'Token geçerli',
        data: {
          user: {
            id: user.userId,
            username: user.username,
            role: user.role,
            branchId: user.branchId
          }
        }
      });

    } catch (error) {
      console.error('Verify token controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Token doğrulama hatası',
        code: 'TOKEN_VERIFICATION_FAILED'
      });
    }
  };

  /**
   * Validation hatalarını formatla
   */
  private formatValidationErrors(error: ZodError): string[] {
    return (error as any).errors.map((err: any) => `${err.path.join('.')}: ${err.message}`);
  }

  /**
   * Device bilgilerini çıkar
   */
  private extractDeviceInfo(req: Request): string {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    return `IP: ${ip}, User-Agent: ${userAgent}`;
  }

  /**
   * Hata koduna göre HTTP status code belirle
   */
  private getStatusCodeForError(errorCode: string): number {
    switch (errorCode) {
      case AuthErrorCodes.INVALID_CREDENTIALS:
      case AuthErrorCodes.INVALID_PIN:
        return 401; // Unauthorized
      
      case AuthErrorCodes.USER_NOT_FOUND:
        return 404; // Not Found
      
      case AuthErrorCodes.USER_INACTIVE:
        return 403; // Forbidden
      
      case AuthErrorCodes.TOO_MANY_ATTEMPTS:
        return 429; // Too Many Requests
      
      case AuthErrorCodes.SESSION_CREATION_FAILED:
      case AuthErrorCodes.TOKEN_GENERATION_FAILED:
        return 500; // Internal Server Error
      
      default:
        return 400; // Bad Request
    }
  }
}
