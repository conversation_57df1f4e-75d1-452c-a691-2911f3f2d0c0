import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('🔧 Test kullanıcısı oluşturuluyor...');

    // 1. Test şirketi oluştur
    const company = await prisma.company.upsert({
      where: { taxNumber: '1234567890' },
      update: {},
      create: {
        name: 'Test Restaurant',
        taxNumber: '1234567890',
        taxOffice: 'Test Vergi Dairesi',
        address: 'Test Adres',
        phone: '0555 123 4567',
        email: '<EMAIL>'
      }
    });

    console.log('✅ Test şirketi oluşturuldu:', company.name);

    // 2. Test şubesi oluştur
    const branch = await prisma.branch.upsert({
      where: { 
        companyId_code: {
          companyId: company.id,
          code: 'MAIN'
        }
      },
      update: {},
      create: {
        companyId: company.id,
        name: '<PERSON>',
        code: '<PERSON><PERSON>',
        address: '<PERSON> <PERSON> Ad<PERSON>',
        phone: '0555 123 4567'
      }
    });

    console.log('✅ Test şubesi oluşturuldu:', branch.name);

    // 3. Test kullanıcıları oluştur
    const users = [
      {
        username: 'admin',
        password: 'admin123',
        pin: '123456',
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN' as const,
        email: '<EMAIL>'
      },
      {
        username: 'kasiyer1',
        password: 'kasiyer123',
        pin: '567890',
        firstName: 'Ahmet',
        lastName: 'Kasiyer',
        role: 'CASHIER' as const,
        email: '<EMAIL>'
      },
      {
        username: 'garson1',
        password: 'garson123',
        pin: '999888',
        firstName: 'Mehmet',
        lastName: 'Garson',
        role: 'WAITER' as const,
        email: '<EMAIL>'
      }
    ];

    for (const userData of users) {
      // Şifre ve PIN'i hashle
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      const hashedPin = await bcrypt.hash(userData.pin, 12);

      const user = await prisma.user.upsert({
        where: { username: userData.username },
        update: {
          password: hashedPassword,
          pin: hashedPin,
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: userData.role,
          email: userData.email
        },
        create: {
          companyId: company.id,
          branchId: branch.id,
          username: userData.username,
          password: hashedPassword,
          pin: hashedPin,
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: userData.role,
          email: userData.email
        }
      });

      console.log(`✅ Kullanıcı oluşturuldu: ${user.username} (PIN: ${userData.pin})`);
    }

    console.log('\n🎉 Test verileri başarıyla oluşturuldu!');
    console.log('\n📋 Test Kullanıcıları:');
    console.log('┌─────────────┬────────┬─────────┬──────────┐');
    console.log('│ Kullanıcı   │ PIN    │ Rol     │ Şifre    │');
    console.log('├─────────────┼────────┼─────────┼──────────┤');
    console.log('│ admin       │ 123456 │ ADMIN   │ admin123 │');
    console.log('│ kasiyer1    │ 567890 │ CASHIER │ kasiyer123│');
    console.log('│ garson1     │ 999888 │ WAITER  │ garson123│');
    console.log('└─────────────┴────────┴─────────┴──────────┘');
    console.log('\n🚀 Artık PIN ile giriş yapabilirsiniz!');

  } catch (error) {
    console.error('❌ Hata:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Script'i çalıştır
createTestUser();
