---
group: Design Patterns
type: Principles
order: 4
title: Repetition
---

The same elements keep repeating in the whole interface, which not only could lower the user's learning cost effectively, but also help user recognize the relevance between these elements.

---

## Repetitive elements

<ImagePreview>
<img class="preview-img" alt="Example of repetitive wireframe" src="https://gw.alipayobjects.com/zos/rmsportal/VkUeJYlTTseLCyUGeXZV.png">
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="Example of repetitive design elements" src="https://gw.alipayobjects.com/zos/rmsportal/HXvcTaEbEWWFaQbiEpLg.png">
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="Example of repetitive of formats" src="https://gw.alipayobjects.com/zos/rmsportal/DYDGrgkbdFEbcVRuJcjH.png">
</ImagePreview>

<ImagePreview>
The repetitive element may be a thick rule(line), a wireframe, color, design elements, particular format, spatial relationships, etc.
</ImagePreview>
