import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthService } from '../services/auth.service';
import { AuthController } from '../controllers/auth.controller';
import { AuthMiddleware, pinLoginRateLimit } from '../middlewares/auth.middleware';

const router = express.Router();

// Dependency injection
const prisma = new PrismaClient();
const authService = new AuthService(prisma);
const authController = new AuthController(authService);
const authMiddleware = new AuthMiddleware(authService);

// ==================== PUBLIC ROUTES ====================

/**
 * POST /api/auth/pin-login
 * PIN ile kullanıcı girişi
 */
router.post('/pin-login',
  pinLoginRateLimit, // Rate limiting
  authController.pinLogin
);

/**
 * GET /api/auth/users
 * Aktif kull<PERSON> listesi (PIN giriş için)
 */
router.get('/users', authController.getUsers);

// ==================== PROTECTED ROUTES ====================

/**
 * POST /api/auth/logout
 * Kullanıcı çıkışı
 */
router.post('/logout',
  authMiddleware.verifyToken,
  authController.logout
);

/**
 * GET /api/auth/verify
 * Token doğrulama
 */
router.get('/verify',
  authMiddleware.verifyToken,
  authController.verifyToken
);

/**
 * GET /api/auth/profile
 * Kullanıcı profil bilgileri
 */
router.get('/profile',
  authMiddleware.verifyToken,
  async (req, res) => {
    try {
      const user = req.user;
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Kimlik doğrulama gerekli'
        });
        return;
      }

      // Kullanıcı detaylarını getir
      const userDetails = await prisma.user.findUnique({
        where: { id: user.userId },
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          role: true,
          branchId: true,
          lastLoginAt: true,
          createdAt: true,
          branch: {
            select: {
              id: true,
              name: true,
              code: true
            }
          }
        }
      });

      if (!userDetails) {
        res.status(404).json({
          success: false,
          message: 'Kullanıcı bulunamadı'
        });
        return;
      }

      res.json({
        success: true,
        data: userDetails
      });

    } catch (error) {
      console.error('Profile fetch error:', error);
      res.status(500).json({
        success: false,
        message: 'Profil bilgileri alınamadı'
      });
    }
  }
);

// ==================== ADMIN ROUTES ====================

/**
 * POST /api/auth/set-pin/:userId
 * Kullanıcı PIN'i belirleme (sadece admin)
 */
router.post('/set-pin/:userId',
  authMiddleware.verifyToken,
  authMiddleware.requireRole(['SUPER_ADMIN', 'ADMIN']),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const { pin } = req.body;

      // PIN validation
      if (!pin || !/^\d{4,6}$/.test(pin)) {
        res.status(400).json({
          success: false,
          message: 'PIN 4-6 haneli rakam olmalıdır'
        });
        return;
      }

      // PIN'i hashle
      const hashedPin = await authService.hashPin(pin);

      // Kullanıcıyı güncelle
      await prisma.user.update({
        where: { id: userId },
        data: { pin: hashedPin }
      });

      res.json({
        success: true,
        message: 'PIN başarıyla belirlendi'
      });

    } catch (error) {
      console.error('Set PIN error:', error);
      res.status(500).json({
        success: false,
        message: 'PIN belirleme hatası'
      });
    }
  }
);

/**
 * GET /api/auth/sessions
 * Aktif oturumları listele (sadece admin)
 */
router.get('/sessions',
  authMiddleware.verifyToken,
  authMiddleware.requireRole(['SUPER_ADMIN', 'ADMIN']),
  async (req, res) => {
    try {
      const sessions = await prisma.session.findMany({
        where: {
          endedAt: null // Sadece aktif oturumlar
        },
        include: {
          user: {
            select: {
              username: true,
              firstName: true,
              lastName: true,
              role: true
            }
          }
        },
        orderBy: {
          startedAt: 'desc'
        }
      });

      res.json({
        success: true,
        data: sessions
      });

    } catch (error) {
      console.error('Sessions fetch error:', error);
      res.status(500).json({
        success: false,
        message: 'Oturum listesi alınamadı'
      });
    }
  }
);

export default router;
