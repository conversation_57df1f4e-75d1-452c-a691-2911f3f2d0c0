import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function seedUsers() {
  try {
    console.log('🌱 Test kullanıcıları oluşturuluyor...');

    // Önce bir company oluştur (eğer yoksa)
    let company = await prisma.company.findFirst();
    if (!company) {
      company = await prisma.company.create({
        data: {
          name: 'Aleo Cafe & Resto',
          taxNumber: '1234567890',
          taxOffice: 'Merkez',
          address: 'Test Adres',
          phone: '+90 ************',
          email: '<EMAIL>'
        }
      });
      console.log('✅ Company oluşturuldu:', company.name);
    }

    // Bir branch oluştur (eğer yoksa)
    let branch = await prisma.branch.findFirst();
    if (!branch) {
      branch = await prisma.branch.create({
        data: {
          companyId: company.id,
          code: '<PERSON><PERSON>',
          name: '<PERSON>',
          address: '<PERSON> <PERSON>',
          phone: '+90 ************'
        }
      });
      console.log('✅ Branch oluşturuldu:', branch.name);
    }

    // Test kullanıcıları
    const testUsers = [
      {
        username: 'intan.fauziah',
        password: 'password123',
        pin: '123456',
        firstName: 'Intan',
        lastName: 'Fauziah',
        role: 'CASHIER',
        email: '<EMAIL>'
      },
      {
        username: 'ahmad.kusuma',
        password: 'password123',
        pin: '234567',
        firstName: 'Ahmad',
        lastName: 'Kusuma',
        role: 'WAITER',
        email: '<EMAIL>'
      },
      {
        username: 'nasih.stevens',
        password: 'password123',
        pin: '345678',
        firstName: 'Nasih',
        lastName: 'Stevens',
        role: 'BRANCH_MANAGER',
        email: '<EMAIL>'
      },
      {
        username: 'elsa.peters',
        password: 'password123',
        pin: '456789',
        firstName: 'Elsa',
        lastName: 'Peters',
        role: 'CASHIER',
        email: '<EMAIL>'
      }
    ];

    for (const userData of testUsers) {
      // Kullanıcı zaten var mı kontrol et
      const existingUser = await prisma.user.findUnique({
        where: { username: userData.username }
      });

      if (existingUser) {
        console.log(`⚠️  Kullanıcı zaten mevcut: ${userData.username}`);
        continue;
      }

      // Şifre ve PIN'i hashle
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      const hashedPin = await bcrypt.hash(userData.pin, 12);

      // Kullanıcıyı oluştur
      const user = await prisma.user.create({
        data: {
          companyId: company.id,
          branchId: branch.id,
          username: userData.username,
          password: hashedPassword,
          pin: hashedPin,
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: userData.role as any,
          email: userData.email,
          active: true
        }
      });

      console.log(`✅ Kullanıcı oluşturuldu: ${user.firstName} ${user.lastName} (${user.username}) - PIN: ${userData.pin}`);
    }

    console.log('🎉 Tüm test kullanıcıları başarıyla oluşturuldu!');
    console.log('\n📋 Test Kullanıcıları:');
    console.log('1. intan.fauziah - PIN: 123456 (CASHIER)');
    console.log('2. ahmad.kusuma - PIN: 234567 (WAITER)');
    console.log('3. nasih.stevens - PIN: 345678 (BRANCH_MANAGER)');
    console.log('4. elsa.peters - PIN: 456789 (CASHIER)');

  } catch (error) {
    console.error('❌ Seed işlemi başarısız:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Script'i çalıştır
seedUsers();
