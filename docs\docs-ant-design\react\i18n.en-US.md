---
group:
  title: Advanced
order: 5
title: Internationalization
---

The default language of `antd` is currently English. If you wish to use other languages, follow the instructions below.

## ConfigProvider

`antd` provides a React Component [ConfigProvider](/components/config-provider) for configuring antd locale text globally.

```jsx
import { ConfigProvider } from 'antd';
import frFR from 'antd/locale/fr_FR';

return (
  <ConfigProvider locale={frFR}>
    <App />
  </ConfigProvider>
);
```

You can see the complete configuration here: [ConfigProvider](/components/config-provider).

Note: `fr_FR` is the filename, the following table also follows the same rules.

The following languages are currently supported:

### Supported languages:

| Language                 | Filename |
| ------------------------ | -------- |
| Arabic                   | ar_EG    |
| Azerbaijani              | az_AZ    |
| Bulgarian                | bg_BG    |
| Bangla (Bangladesh)      | bn_BD    |
| Belarusian               | by_BY    |
| Catalan                  | ca_ES    |
| Czech                    | cs_CZ    |
| Danish                   | da_DK    |
| German                   | de_DE    |
| Greek                    | el_GR    |
| English (United Kingdom) | en_GB    |
| English                  | en_US    |
| Spanish                  | es_ES    |
| Basque                   | eu_ES    |
| Estonian                 | et_EE    |
| Persian                  | fa_IR    |
| Finnish                  | fi_FI    |
| French (Belgium)         | fr_BE    |
| French (Canada)          | fr_CA    |
| French (France)          | fr_FR    |
| Irish (Ireland)          | ga_IE    |
| Galician (Spain)         | gl_ES    |
| Hebrew                   | he_IL    |
| Hindi                    | hi_IN    |
| Croatian                 | hr_HR    |
| Hungarian                | hu_HU    |
| Armenian                 | hy_AM    |
| Indonesian               | id_ID    |
| Italian                  | it_IT    |
| Icelandic                | is_IS    |
| Japanese                 | ja_JP    |
| Georgian                 | ka_GE    |
| Kurdish (Kurmanji)       | kmr_IQ   |
| Kannada                  | kn_IN    |
| Kazakh                   | kk_KZ    |
| Khmer                    | km_KH    |
| Korean                   | ko_KR    |
| Lithuanian               | lt_LT    |
| Latvian                  | lv_LV    |
| Macedonian               | mk_MK    |
| Malayalam (India)        | ml_IN    |
| Mongolian                | mn_MN    |
| Malay (Malaysia)         | ms_MY    |
| Burmese                  | my_MM    |
| Norwegian                | nb_NO    |
| Nepali                   | ne_NP    |
| Dutch (Belgium)          | nl_BE    |
| Dutch                    | nl_NL    |
| Polish                   | pl_PL    |
| Portuguese (Brazil)      | pt_BR    |
| Portuguese               | pt_PT    |
| Romanian                 | ro_RO    |
| Russian                  | ru_RU    |
| Sinhalese / Sinhala      | si_LK    |
| Slovak                   | sk_SK    |
| Serbian                  | sr_RS    |
| Slovenian                | sl_SI    |
| Swedish                  | sv_SE    |
| Tamil                    | ta_IN    |
| Thai                     | th_TH    |
| Turkish                  | tr_TR    |
| Turkmen                  | tk_TK    |
| Urdu (Pakistan)          | ur_PK    |
| Ukrainian                | uk_UA    |
| Uzbek(latn)              | uz_UZ    |
| Vietnamese               | vi_VN    |
| Chinese (Simplified)     | zh_CN    |
| Chinese (Traditional)    | zh_HK    |
| Chinese (Traditional)    | zh_TW    |

See more usage at [ConfigProvider](/components/config-provider).

## Adding new language

If your language is not in above list, feel free to create a locale package based on the [en_US](https://github.com/ant-design/ant-design/blob/master/components/locale/en_US.ts) language pack and send us a pull request. For reference, you can refer to the pull request of adding the [Azerbaijani](https://github.com/ant-design/ant-design/pull/21387) language as a sample.

Do it step by step:

1. Fork `antd` and git clone to local, switch to `feature` branch, pull it to make sure it's up-to-date, create a new branch based on `feature` branch, all work will be done in it.

   ```bash
   <NAME_EMAIL>:<your organization>/ant-design.git
   cd ant-design/
   git remote <NAME_EMAIL>:ant-design/ant-design.git
   git checkout -b <your new branch name> upstream/feature
   ```

2. Add the language support for [rc-picker](https://github.com/react-component/picker), for example [this](https://github.com/react-component/picker/blob/master/src/locale/en_US.ts).
3. Add the language support for [rc-pagination](https://github.com/react-component/pagination), for example [this](https://github.com/react-component/pagination/blob/master/src/locale/en_US.ts).
4. Wait for `rc-picker` and `rc-pagination` to release the new version containing the above.
5. Update the `rc-picker` and `rc-pagination` versions in `antd` and add the remaining other necessary content for the language. for example [Azerbaijani PR](https://github.com/ant-design/ant-design/pull/21387).
6. Add a test case for the language in [index.test.tsx](https://github.com/ant-design/ant-design/blob/master/components/locale/__tests__/index.test.tsx).
7. update snapshots, you may also need to delete `node_modules`, lock files (`yarn.lock` or `package-lock.json`) and reinstall at first.

   ```bash
   npm run test -- components/locale -u
   ```

8. Add the language to i18n list [docs/react/i18n.en-US.md](https://github.com/ant-design/ant-design/blob/master/docs/react/i18n.en-US.md) and [docs/react/i18n.zh-CN.md](https://github.com/ant-design/ant-design/blob/master/docs/react/i18n.zh-CN.md).
9. Watch out the CI status, and if it failed, look at the logs and make some changes until it all passes.
10. Ok, now everything is ready for review.
