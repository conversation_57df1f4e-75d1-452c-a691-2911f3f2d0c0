---
group: Design Patterns
type: Global Rules
order: 5
title: Copywriting
---

In the interface, we need to resonate with users through dialogue. Accurate and clear words are easy to understand, and a suitable tone can build a sense of trust easily. Therefore, in the interface design, copywriting should be taken seriously. There are some points to note when using and writing copy:

- Consider from the user's point of view
- Express consistently
- Place important information in prominent positions
- Professional, accurate and complete
- Concise, friendly and positive

---

## Language

In the interface, copywriting is the basis of our communication with users. Therefore, the expression of words should be carefully deliberate and designed. With clear, accurate, and concise copywriting, the user experience can be more user-friendly.

### Articulate foothold

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/Ik0zIDUblJ/85996a34-3add-481b-b164-fc82e0b10bba.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/3hg1I8dJ%24W/6765f193-ae21-463e-be9a-609fafc76c8d.png">
</ImagePreview>

When expressing content, the focus should be on users -- what they can do with your product? Not what you and your product are doing for them. The foothold of content representation is very important. Since it is user-centred design, copywriting should be user-centred as much as possible.

> Note: Use "we" to communicate with users when they are reporting questions, suggestions or complaints to the systems, such as "We will consider your complaint.".

### Concise statement

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/Xz2N2QXNz1/d4c43668-06d5-4dca-8c54-49402dfde7b7.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/3To5rdNTVQ/1ebf6213-a8d7-401a-ae69-f99bf2b294f0.png">
</ImagePreview>

Omit useless words and do not repeat facts known to users. In most situations, there is no need for the interface to describe all the details. Try to provide short and accessible content.

### Use words familiar to the user

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/OdDMiTnRII/fb2c6dcc-881b-42ac-b83e-4d3a90ff88bc.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/qnPRlCpzZz/388f7772-d962-49ee-a103-582e3ff6c0ca.png" description="Stand from the user's point of view and say what the user is familiar with.">
</ImagePreview>

Use simple, direct and easy-to-understand words. Indirect, ambiguous, obscure, and overly "refined" copywriting will increase user's cognitive load.

### Express consistently

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/OP88j1SDf6/ef562312-9a51-456b-8e53-a127c9f25e80.png" description="The word order is the same on the same page and in the same area.">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/9DiXkPap0V/6e03b9cc-f594-4bf3-9c15-913980c6175a.png" description="Inconsistent word orders will take users' more effort to comprehend.">
</ImagePreview>

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/antfincdn/3zhhAZYGSc/ba1c9133-abb3-4f67-b116-544e9ad0cbad.png" alt="The operation name and the target page title should be the same.">
</ImagePreview>

- Use consistent words that describe the same thing;
- Use consistent grammar, language and word orders of the context;
- Use consistent operation names and page titles.

### Place important information in a prominent position

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/ivTpE4BgSU/16dc61e6-f85c-43d1-9abd-86b046730a6a.png" description="Put important information first in a limited space (or make it more visible with highlights, blank space, etc.).">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/X%24DPUktJbT/a19e0548-1bdd-49f0-ab93-5358b5cf1a4c.png" description="The content that the user cares most is hidden in the paragraph, which is not easy to find out.">
</ImagePreview>

Let users see the most important content at first glance.

> Note: When considering security issues, private information can be adjusted to "visible after click".

### Express completely and directly

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/70QcFGdW%26H/6e0d94da-3ce9-471d-b57c-9f8f038d409f.png" description="Users can learn about the benefits of the settings.">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/eKXgIakJPk/1cb7213d-61c4-41f2-84f0-256bcf436fe3.png" description="Users can't get the meaning of the settings.">
</ImagePreview>

When we want the user to take an action, we should focus on what the user can get and how he/she feels. Telling users the purpose or importance of the action can make them more willing to perform it.

<br />

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/7oHuZX%26n5h/8fef2777-0841-4ea7-a274-75d6e78b6c15.png" description='Compared to "failure", "unable to complete" is a more objective result and easier for users to accept. Users need to know what to do next in the event of a problem.'>
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/oqI9DerB%26W/857e91be-ed75-4b70-aca9-811581080edd.png" description='Do not inform users "failure" coldly for abnormal situations.'>
</ImagePreview>

Error reporting is a common feature in the UI, and it is an important part of user experience. When the user inputs  the wrong content, your error message should be consistent with the user's cognition, and expressed in an easy-to-understand way.

### Use words precisely and completely

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/Txu5VxBFTF/ef2a61a7-8f65-4001-8018-53aa2fccb28d.png" description="Complete expression.">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/ZG%26eSSivMP/c6b8413b-6651-4b6c-bbbc-5464e00761fc.png" description="Incomplete, ambiguous, or too colloquial.">
</ImagePreview>

Use general basic words normatively. Spell correctly, express completely. Professional terms should be accurate, according to industry standards; the expression of time must be clear.

<br />

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/oo3%24YkT8I5/1d7cfb4b-2a15-4f02-a110-17e24f837c1c.png" description="The expression of time is accurate and complete.">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/Np4rjq6bhw/8b13eab9-5441-43f5-8363-acc6d42b5cd0.png" description='The description of time is not a specific "day" or "month", which confuses users.'>
</ImagePreview>

## Tone

Language defines content, while emotions and atmosphere are expressed more in tone. The same content can be expressed in different tones to different users. Take an example, to professional operators and new users, we should use different copywriting.

### Bring each other closer

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/j%267kjvP3kD/0d245e49-dba2-452c-93c3-68d6cb7c094f.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/7QsjvP%24TNj/f1310422-92d6-4ee2-b60a-21619e5bab9f.png">
</ImagePreview>

Don't refer to the user by using "my" and "your" in the same phrase.

> Note: To avoid confusing the users, don't mix first person("I", "me", or "my") and second person("you", "your") in the same sentence.

### Be friendly and respectful

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/EUV9qTHkWb/4a3cfd16-8eaa-44e3-ae1b-caf4b7bbc61a.png" description="Guide the user to enter the content correctly.">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/ssgx2B5f2W/f7327c68-708c-4707-b14f-d1951616c9f9.png" description="「can't」, 「don't」 make user feel a sense of command and pressure.">
</ImagePreview>

Give users support and encouragement, not commands or pressure. If you want to keep your users, don't blame them when things go wrong. Focus on solving problems, not blaming.

### Do not be too extreme

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/mxmAx3KkzZ/2279654a-e42b-4db9-b6dd-2c2d00eaf501.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/dZxbeLAu1q/061fcf43-4dd4-49f5-a00f-61429d22be92.png" description='"Never" is too absolute and makes users feel uncomfortable.'>
</ImagePreview>

Don't use too absolute expression that will make the user uncomfortable.

## Capitalization and punctuation

### Uppercase and lowercase

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/pupl5xDdmR/32d6f262-049d-4ecb-a0d0-d2923f395fcd.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/VVdJdMspm2/9e64ab85-9786-43df-b134-7f4a505d76b1.png">
</ImagePreview>

When using the full name of the product, capitalize the first letter of each word. Write the abbreviations of product names in capital, such as ESC, SLB, etc.

> People are much more used to reading words in lowercase letters, those are what our brains find easiest to scan and instantly absorb. Please avoid capitalizing whole words or phrases.

<br />

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/gdd3ZqLRuG/7cb448ce-bd5d-4354-af54-ea27b8491c6a.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/6ixy2%26KCzS/c742ea07-d983-4575-bb67-0f132b2787ab.png">
</ImagePreview>

Use the correct case.

<br />

<ImagePreview>
<img class="preview-img good no-padding" alt="Recommend" src="https://gw.alipayobjects.com/zos/antfincdn/WUKYyB%24FYw/36308410-e097-4ef0-ae88-8a57ba440ff4.png">
<img class="preview-img bad no-padding" alt="Not recommend" src="https://gw.alipayobjects.com/zos/antfincdn/yYgGa9h4U2/43ba0f31-fbea-4635-865f-a5ace6a19cdb.png">
</ImagePreview>

Use sentence capital case in headlines, titles, labels, menu items, buttons, etc.

### Arabic numbers

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/6Qg%24mrbKB%24/fe25e8db-3377-44e4-a3e6-61d0fe062a93.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/QyMfBb3I29/4cfc9ef7-7f2e-4441-8031-e05db349b7ce.png">
</ImagePreview>

Users perceive numbers faster. Numbers transmit information more effectively than words.

### Omit unnecessary punctuation

To help users scan the text more efficiently, unnecessary periods can be omitted. No need to use punctuation when the following elements appear alone:

- Label
- Title
- Tips under the input box
- Text in tooltip component
- Sentences in the table

<br />

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/tJ9bT2%24Oky/43dd91ce-694a-4ce9-bfeb-adb020c257f9.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/YgQfEmqiIh/781c6045-31f9-4e64-b2bf-17feb94498c8.png">
</ImagePreview>

The following elements need to be punctuated when they appear separately:

- Multiple sentences or paragraphs
- Any sentence before a link

### Use exclamation marks with caution

<ImagePreview>
<img class="preview-img good no-padding" alt="Correct Example" src="https://gw.alipayobjects.com/zos/antfincdn/D3I1Y4%26mPt/13c2bf6a-c822-49c7-9959-46fdc3a07daf.png">
<img class="preview-img bad no-padding" alt="Wrong Example" src="https://gw.alipayobjects.com/zos/antfincdn/JhP0VxI%24Vb/230c13d9-e26e-4cff-8b80-bebf41f13b0b.png">
</ImagePreview>

The exclamation mark will make the tone appear too excited, and it will easily make the atmosphere too tense.

> Note: When expressing greetings or congratulations to the user, use "!" is reasonable, such as" Welcome back to the community! ".
