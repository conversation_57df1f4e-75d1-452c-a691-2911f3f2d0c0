---
group:
  title: 其他
  order: 3
order: 0
title: 社区精选组件
---

`antd` 是基于 Ant Design 设计规范实现的 [高质量 React 组件库](/components/overview)，我们倾向于只提供符合该规范且带有视觉展现的 UI 组件，也尽量不重复造轮子。我们推荐使用以下社区已有的优秀实现，与 antd 形成互补。

| 类型 | 推荐组件 |
| --- | --- |
| 可视化图表 | [Ant Design Charts](https://charts.ant.design) [AntV 数据可视化解决方案](https://antv.vision/zh) [reactflow](https://reactflow.dev/) |
| React Hooks 库 | [ahooks](https://github.com/alibaba/hooks) |
| 表单 | [ProForm](https://procomponents.ant.design/components/form) [Formily](https://github.com/alibaba/formily) [react-hook-form](https://github.com/react-hook-form/react-hook-form) [formik](https://github.com/formium/formik) |
| 路由 | [react-router](https://github.com/ReactTraining/react-router) |
| 布局 | [react-grid-layout](https://github.com/react-grid-layout/react-grid-layout) [react-grid-system](https://github.com/sealninja/react-grid-system) [rc-dock](https://github.com/ticlo/rc-dock) |
| 拖拽 | [dnd-kit](https://github.com/clauderic/dnd-kit) [react-beautiful-dnd](https://github.com/atlassian/react-beautiful-dnd/) [react-dnd](https://github.com/gaearon/react-dnd) |
| 代码编辑器 | [@uiw/react-codemirror](https://github.com/uiwjs/react-codemirror) [react-monaco-editor](https://github.com/react-monaco-editor/react-monaco-editor) |
| 富文本编辑器 | [react-quill](https://github.com/zenoamaro/react-quill) |
| JSON 编辑器 | [vanilla-jsoneditor](https://github.com/josdejong/svelte-jsoneditor) |
| JSON 显示器 | [react-json-view](https://github.com/mac-s-g/react-json-view) |
| 拾色器 | [react-colorful](https://github.com/omgovich/react-colorful) [react-color](https://casesandberg.github.io/react-color/) |
| 响应式 | [react-responsive](https://github.com/contra/react-responsive) |
| 复制到剪贴板 | [react-copy-to-clipboard](https://github.com/nkbt/react-copy-to-clipboard) |
| 页面 meta 属性 | [react-helmet-async](https://github.com/staylor/react-helmet-async) |
| 图标 | [react-fontawesome](https://github.com/FortAwesome/react-fontawesome) [react-icons](https://github.com/gorangajic/react-icons) |
| 二维码 | [qrcode.react](https://github.com/zpao/qrcode.react) |
| 顶部进度条 | [react-nprogress](https://github.com/tanem/react-nprogress) |
| 应用国际化 | [FormatJS](https://github.com/formatjs/formatjs) [react-i18next](https://react.i18next.com) |
| 代码高亮 | [react-syntax-highlighter](https://github.com/conorhastings/react-syntax-highlighter) |
| Markdown 渲染 | [react-markdown](https://remarkjs.github.io/react-markdown/) |
| 无限滚动 | [rc-virtual-list](https://github.com/react-component/virtual-list/) [react-infinite-scroll-component](https://github.com/ankeetmaini/react-infinite-scroll-component) |
| 地图 | [google-map-react](https://github.com/istarkov/google-map-react) [@uiw/react-amap 高德地图](https://github.com/uiwjs/react-amap) |
| 视频播放 | [react-player](https://github.com/CookPete/react-player) [video-react](https://github.com/video-react/video-react) [video.js](https://docs.videojs.com/tutorial-react.html) |
| 右键菜单 | [react-contexify](https://github.com/fkhadra/react-contexify) |
| Emoji | [emoji-picker-react](https://github.com/ealush/emoji-picker-react) [emoji-mart](https://github.com/missive/emoji-mart) |
| 分割面板 | [react-split-pane](https://github.com/tomkp/react-split-pane) [react-resizable-panels](https://github.com/bvaughn/react-resizable-panels) |
| 图片裁切 | [antd-img-crop](https://github.com/nanxiaobei/antd-img-crop) [react-image-crop](https://github.com/DominicTobias/react-image-crop) |
| 关键字高亮 | [react-highlight-words](https://github.com/bvaughn/react-highlight-words) |
| 文字轮播 | [react-text-loop-next](https://github.com/samarmohan/react-text-loop-next) [react-fast-marquee](https://github.com/justin-chu/react-fast-marquee) |
| 动画 | [motion](https://github.com/framer/motion) [Ant Motion](https://motion.ant.design/components/tween-one) [react-spring](https://github.com/pmndrs/react-spring) |
| 页脚 | [rc-footer](https://github.com/react-component/footer) |
| 数字/金额 | [react-number-format](https://github.com/s-yadav/react-number-format) [react-currency-input-field](https://github.com/cchanxzy/react-currency-input-field) |
| 移动端探测 | [react-device-detect](https://github.com/duskload/react-device-detect) |
| 应用程序框架 | [umi](https://github.com/umijs/umi) [remix](https://github.com/remix-run/remix) [refine](https://github.com/pankod/refine) |
| Flow 流 | [pro-flow](https://github.com/ant-design/pro-flow) [react-flow](https://github.com/wbkd/react-flow) [x6](https://github.com/antvis/x6) |
| 电话输入 | [react-phone-number-input](https://gitlab.com/catamphetamine/react-phone-number-input) [antd-phone-input](https://github.com/ArtyomVancyan/antd-phone-input/) |
| AI 对话应用 | [Ant Design X](https://github.com/ant-design/x) |
| PDF | [react-pdf](https://github.com/diegomura/react-pdf) [@react-pdf/renderer](https://github.com/diegomura/react-pdf) |
| React 手势库 | [use-gesture](https://use-gesture.netlify.app) |

## 推荐产品 ✨

还有一些常用的前端/设计/产品相关的工具推荐给大家使用。

| 类型 | 推荐产品 |
| --- | --- |
| 知识管理 | [🐦 语雀](https://www.yuque.com/?chInfo=ch_antd) |
| 图标 | [阿里巴巴矢量图标库](https://www.iconfont.cn) |
| Sketch 插件 | [Kitchen](https://kitchen.alipay.com) |
| 在线代码编辑 | [stackblitz](https://stackblitz.com) [codesandbox](https://codesandbox.io/) [codepen](https://codepen.io/) |

<style>
.markdown table td:first-child {
  width: 20%;
  font-weight: 500;
}
.markdown table td > a:not(:last-child) {
  margin-inline-end: 18px;
}
.markdown table td > a:not(:last-child)::after {
  position: absolute;
  margin: 0 6px 0 8px;
  color: #bbb;
  content: '|';
  pointer-events: none;
}
</style>
