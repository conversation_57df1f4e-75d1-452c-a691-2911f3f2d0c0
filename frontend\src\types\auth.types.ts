// ==================== USER TYPES ====================

export interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  branchId: string | null;
  email?: string;
  phone?: string;
  active: boolean;
  lastLoginAt?: string;
  createdAt: string;
}

export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  BRANCH_MANAGER = 'BRANCH_MANAGER',
  CASHIER = 'CASHIER',
  WAITER = 'WAITER',
  KITCHEN = 'KITCHEN',
  REPORTER = 'REPORTER'
}

// ==================== AUTH TYPES ====================

export interface LoginRequest {
  username: string;
  pin: string;
  deviceInfo?: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: User;
    session: {
      id: string;
      startedAt: string;
    };
  };
}

export interface AuthError {
  success: false;
  message: string;
  code?: string;
  errors?: string[];
}

// ==================== PIN LOGIN TYPES ====================

export interface PinLoginState {
  users: User[];
  selectedUser: User | null;
  pin: string;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  currentUser: User | null;
  token: string | null;
}

export interface PinLoginActions {
  setUsers: (users: User[]) => void;
  setSelectedUser: (user: User | null) => void;
  setPin: (pin: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setCurrentUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
  clearPin: () => void;
  reset: () => void;
}

// ==================== COMPONENT PROPS ====================

export interface UserListProps {
  users: User[];
  selectedUser: User | null;
  onUserSelect: (user: User) => void;
  loading?: boolean;
}

export interface PinInputProps {
  value: string;
  onChange: (value: string) => void;
  length?: number;
  disabled?: boolean;
  error?: boolean;
}

export interface NumericKeypadProps {
  onNumberClick: (number: string) => void;
  onBackspace: () => void;
  onClear: () => void;
  disabled?: boolean;
}

export interface StartShiftButtonProps {
  onClick: () => void;
  loading?: boolean;
  disabled?: boolean;
  selectedUser: User | null;
}
