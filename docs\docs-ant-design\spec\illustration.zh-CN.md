---
order: 5
title: 图形化
---

<div style="text-align:center;">
  <img alt="General" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*WzMpTIP8R6gAAAAAAAAAAABkARQnAQ" />
</div>

## 项目背景

图形化是品牌识别度的关键核心元素，在互联网产品、线下物料中无处不在。与单纯的文案信息不同，图形化在直观描述固有信息的同时塑造情感背景，使用户更具沉浸感和共情性。提升产品用户体验的同时来完成商业目标。图形化的风格缤纷复杂，插画师的个人风格明显，不同的设计师在图形化的工作协同中，风格很难复现，而单纯由一名插画师去完成整体业务的图形化也存在一定风险。所以图形化体系在保持品牌一致性和提升工作效率、规避风险上显得尤为重要。

<div style="text-align:center;">
  <img alt="Background" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*rSUBTL8hv9sAAAAAAAAAAABkARQnAQ" />
</div>

## 设计原则

从最底层的设计价值观到最顶层的设计方法，HiTu 沿袭了 Ant Design 的 ETCG 的设计思路，将图形化资产组件化，分可形成模板，合则可拼搭成案例。为设计值提供强大的灵活性和定制性。

<div style="text-align:center;">
  <img alt="Design principle" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*WKEzS5-_zYAAAAAAAAAAAABkARQnAQ" />
</div>

#### HiTu 金字塔模型

基于科技、确定、自然、未来的四层品牌策略，我们将抽象的概念具象化。代表的技术底层的科技能力为产品、体验和未来提供技术支撑和可能性。所以图例一一对应，我们具象化了人物和产品，体验及未来之间的关系。他们之间不同的组合方式可以满足不同的业务场景诉求。

<div style="text-align:center;">
  <img alt="Pyramid model" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*gCoSS5DaCNEAAAAAAAAAAABkARQnAQ" />
</div>

## 颜色

### 海兔色板

<ImagePreview>
<img class="preview-img no-padding" description="海兔色板" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*5ZE6RrjW-jQAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

海兔的色彩配色体系是基于 Ant Design 色板的场景应用，与 UI 色彩体系的应用会有所不同。图形化的配色体系会相对更加的灵活可变。基于 Ant Design 的基础色板我们进行了明度的调整，扩大图形化设计的宽容度，使用选择时候更加高效易用，由于是出自 Ant Design 的色彩体系，和其他 UI 资产完美兼容。

### 海兔默认资产颜色

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*0Dv9Rrp7GtMAAAAAAAAAAAAAARQnAQ" />
</ImagePreview>

通过搜资调研，我们发现在企业级产品中。色彩的使用上蓝色，白色会占很大的比重。我们选取了色板中最具科技感，且代表着探索、钻研感的极客蓝作为我们的主色。

<br />

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*U5L-RKWlmJcAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

基于 Adobe Color 的三元取色工具，和 3D 立体马赛克球，结合我们自己的海兔色板。可以轻松得到了我们默认版本的基础色板。

<br />

# 设计资产

### 人物组件

<ImagePreview>
<img class="preview-img" alt="基于自然的人物头身比例" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*rm9JRIqTmPgAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

基于自然的设计原则，我们不推荐使用 Q 版卡通和过于夸张艺术化的设计风格。相比之下，接近自然真人头人比例的风格是我们更加推荐的。

<br />

<ImagePreview>
<img class="preview-img" alt="9 种常见职业角色设计" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*Zt7BSI2OL7gAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

同时，我们结合情感化设计，将 9 种常见的职业角色进行人物设计。结合职业本身的一些特质，我们也赋予了他们不同气质特质，来满足各种业务场景的使用。

<br />

<ImagePreview>
<img class="preview-img" alt="角色设计人物分解" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*jUujRJBxU-sAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="HiTu 骨骼系统" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*C3NCT6QHU9QAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="人物姿态复用" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*NZuwQp_vcIQAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

有了基础的人物设计，我们将人物进行分解，然后搭配设计好的骨骼系统。来为人物增加各种姿态动作的复用和延展。

<br />

### 元素组件

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*ph0YSZmq-ekAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="根据业务场景设计的元素组件" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*Z8oxS5ym3PIAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

记忆点源于与众不同，专业感源于整齐划一。元素组件指代的是业务场景中一些业务元素，状态元素图形化的世界缤纷多变，我们希望在保持统一性的同时不去限制设计者的创意空间，元素组件的设计上，我们为了保持一定的韵律感，推荐设计师在 1024\*1024 的网格中绘制组件，且圆角的大小保持 8 的倍数关系。

<br />

# 设计应用

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*puHVQJEe-oIAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*W-PzSadIFeAAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*OJoaQ77tczIAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*IySSSoBaGPYAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

有了丰富的资产组件，我们应该如何使用呢？在 HiTu 的设计使用指引中，我推荐设计者在使用资产的过程中构建 Z 轴向的空间概念，将整幅画面拆分成前景，中景以及背景三个层次，在组件的排放时候，前景凸显重要的元素（如人，核心元素组件等），中间交代所处环境，背景则渲染烘托氛围，在颜色的使用和透明上也是前景的饱和度和透明度最高，逐级降低。

<br />
