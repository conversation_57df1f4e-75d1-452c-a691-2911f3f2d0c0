---
group: Global Styles
order: 6
title: Shadow
---

Shadow originates from the physical phenomenon of reflecting the distance between objects in real life. For the user interface (UI), we often simulate this through element projection to inform the user about the height distance and layer hierarchy between elements.

## Height

Shadows are produced by two surfaces at different levels, and the intensity is determined by the distance between them. Therefore, the height of an object directly affects its shadow. The farther an object is from the ground, the larger and blurrier the shadow becomes. We will divide the system into four UI levels: none, low, medium, and high, each distributed across different height levels, with varying shadow properties.

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*o-OFQK5hJP0AAAAAAAAAAABkARQnAQ" alt="height levels">
</div>

**Layer 0**: When an object is close to the ground, its shadow overlaps completely with the object itself. In the UI, no shadow value is defined for this layer. For example: input boxes.

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*li3zQbxRuOMAAAAAAAAAAABkARQnAQ" alt="height layer 0">
</div>

**Layer 1**: When an object appears at the low level, it enters a floating state when manipulated (hovered, clicked, etc). Once the operation is completed or canceled, the hover state feedback disappears, and the object returns to its original level. For example: card hovering.

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*mXnoSI8MWuEAAAAAAAAAAABkARQnAQ" alt="height layer 1">
</div>

**Layer 2**: When an object appears at the medium level, it expands and follows the relationship with the reference layer. The object opens from elements on the ground and moves with the movement of the elements at that level. For example: dropdown panels.

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*yNOwR6Gqy4MAAAAAAAAAAABkARQnAQ" alt="height layer 2">
</div>

**Layer 3**: When an object appears at the high level, its movement is independent of other levels. For example: dialog boxes.

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*4pFoR4gWL2oAAAAAAAAAAABkARQnAQ" alt="height layer 3">
</div>

## Light Source

The direction of a shadow is determined by the relative position of the light source and the object. Assuming the height of the light source remains constant, the distance between the light source and the object, as well as the distance between the object and the shadow, are directly proportional. The further away the light source, the further away the shadow from the object. In the UI, the direction of shadows is typically represented using the `X, Y` coordinates.

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*KgGmQ79c38oAAAAAAAAAAABkARQnAQ" alt="light source">
</div>

## Shadow Values

As mentioned above, shadows are generated by illumination. The main factors affecting their values are the height of the object and the position of the light source:

1. At different heights, the shadow's color, blur, and area vary. Objects further from the ground produce lighter shadows with higher blur and larger area, while those closer to the ground create darker shadows with lower blur and smaller area.

2. The direction of the projection is primarily determined by the relative position of the light source and the object.

In Ant Design, different shadow directions are used in various contexts:

- Downwards Shadow: mainly used inside components or the components themselves, which is the most common use case.
- Upwards Shadow: mainly applied to bottom navigation or toolbars, etc.
- Leftwards Shadow: mainly used in right-side navigation bars, drawer components, or fixed table headers.
- Rightwards Shadow: mainly used in left-side navigation bars, drawer components, or fixed table headers.

Shadow simulates real-world feedback. To make shadows more realistic, Ant Design adopted a three-layer shadow expression method in version 4.0, making shadows softer and more realistic.

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*1oijTJh2HEIAAAAAAAAAAABkARQnAQ" alt="shadow value">
</div>

### Common Shadow Usage Design Table

**Layer One:**

<table>
  <thead>
    <tr>
      <th>Shadow Type</th>
      <th>Shadow Color (rgba)</th>
      <th>Direction (X, Y)</th>
      <th>Blur</th>
      <th>Spread</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowSpan="3">@shadow-1-up</td>
      <td>rgba(0, 0, 0, 0.16)</td>
      <td>0px, -1px</td>
      <td>2px</td>
      <td>-2px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.12)</td>
      <td>0px, -3px</td>
      <td>6px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.09)</td>
      <td>0px, -5px</td>
      <td>12px</td>
      <td>4px</td>
    </tr>
    <tr>
      <td rowSpan="3">@shadow-1-down</td>
      <td>rgba(0, 0, 0, 0.16)</td>
      <td>0px, 1px</td>
      <td>2px</td>
      <td>-2px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.12)</td>
      <td>0px, 3px</td>
      <td>6px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.09)</td>
      <td>0px, 5px</td>
      <td>12px</td>
      <td>4px</td>
    </tr>
    <tr>
      <td rowSpan="3">@shadow-1-left</td>
      <td>rgba(0, 0, 0, 0.16)</td>
      <td>-1px, 0px</td>
      <td>2px</td>
      <td>-2px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.12)</td>
      <td>-3px, 0px</td>
      <td>6px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.09)</td>
      <td>-5px, 0px</td>
      <td>12px</td>
      <td>4px</td>
    </tr>
    <tr>
      <td rowSpan="3">@shadow-1-right</td>
      <td>rgba(0, 0, 0, 0.16)</td>
      <td>1px, 0px</td>
      <td>2px</td>
      <td>-2px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.12)</td>
      <td>3px, 0px</td>
      <td>6px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.09)</td>
      <td>5px, 0px</td>
      <td>12px</td>
      <td>4px</td>
    </tr>
  </tbody>
</table>

**Layer Two:**

<table>
  <thead>
    <tr>
      <th>Shadow Type</th>
      <th>Shadow Color (rgba)</th>
      <th>Direction (X, Y)</th>
      <th>Blur</th>
      <th>Spread</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowSpan="3">@shadow-2-up</td>
      <td>rgba(0, 0, 0, 0.12)</td>
      <td>0px, -3px</td>
      <td>6px</td>
      <td>-4px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.08)</td>
      <td>0px, -6px</td>
      <td>16px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.05)</td>
      <td>0px, -9px</td>
      <td>28px</td>
      <td>8px</td>
    </tr>
    <tr>
      <td rowSpan="3">@shadow-2-down</td>
      <td>rgba(0, 0, 0, 0.12)</td>
      <td>0px, 3px</td>
      <td>6px</td>
      <td>-4px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.08)</td>
      <td>0px, 6px</td>
      <td>16px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.05)</td>
      <td>0px, 9px</td>
      <td>28px</td>
      <td>8px</td>
    </tr>
    <tr>
      <td rowSpan="3">@shadow-2-left</td>
      <td>rgba(0, 0, 0, 0.12)</td>
      <td>-3px, 0px</td>
      <td>6px</td>
      <td>-4px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.08)</td>
      <td>-6px, 0px</td>
      <td>16px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.05)</td>
      <td>-9px, 0px</td>
      <td>28px</td>
      <td>8px</td>
    </tr>
    <tr>
      <td rowSpan="3">@shadow-2-right</td>
      <td>rgba(0, 0, 0, 0.12)</td>
      <td>3px, 0px</td>
      <td>6px</td>
      <td>-4px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.08)</td>
      <td>6px, 0px</td>
      <td>16px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.05)</td>
      <td>9px, 0px</td>
      <td>28px</td>
      <td>8px</td>
    </tr>
  </tbody>
</table>

**Layer Three:**

<table>
  <thead>
    <tr>
      <th>Shadow Type</th>
      <th>Shadow Color (rgba)</th>
      <th>Direction (X, Y)</th>
      <th>Blur</th>
      <th>Spread</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowSpan="3">@shadow-3-up</td>
      <td>rgba(0, 0, 0, 0.08)</td>
      <td>0px, -6px</td>
      <td>16px</td>
      <td>-8px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.05)</td>
      <td>0px, -9px</td>
      <td>28px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.03)</td>
      <td>0px, -12px</td>
      <td>48px</td>
      <td>16px</td>
    </tr>
    <tr>
      <td rowSpan="3">@shadow-3-down</td>
      <td>rgba(0, 0, 0, 0.08)</td>
      <td>0px, 6px</td>
      <td>16px</td>
      <td>-8px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.05)</td>
      <td>0px, 9px</td>
      <td>28px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.03)</td>
      <td>0px, 12px</td>
      <td>48px</td>
      <td>16px</td>
    </tr>
    <tr>
      <td rowSpan="3">@shadow-3-left</td>
      <td>rgba(0, 0, 0, 0.08)</td>
      <td>-6px, 0px</td>
      <td>16px</td>
      <td>-8px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.05)</td>
      <td>-9px, 0px</td>
      <td>28px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.03)</td>
      <td>-12px, 0px</td>
      <td>48px</td>
      <td>16px</td>
    </tr>
    <tr>
      <td rowSpan="3">@shadow-3-right</td>
      <td>rgba(0, 0, 0, 0.08)</td>
      <td>6px, 0px</td>
      <td>16px</td>
      <td>-8px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.05)</td>
      <td>9px, 0px</td>
      <td>28px</td>
      <td>0px</td>
    </tr>
    <tr>
      <td>rgba(0, 0, 0, 0.03)</td>
      <td>12px, 0px</td>
      <td>48px</td>
      <td>16px</td>
    </tr>
  </tbody>
</table>
