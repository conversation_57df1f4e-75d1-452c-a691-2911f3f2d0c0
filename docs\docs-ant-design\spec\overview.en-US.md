---
group:
  title: Design Patterns
  order: 2
order: 0
title: Overview
---

The use of design patterns in enterprise-level businesses can significantly increase the certainty of the R&D team, save unnecessary design and maintain system consistency, allowing designers to focus on creativity where it is most needed.

Design patterns adhere to Ant Design design values and provide a general solution to recurring design issues in enterprise products. The designer can directly use the design pattern to complete the interface design, or the design pattern can be used as a starting point to derive a more business-specific solution to meet the individual design needs.

At the same time, this is a dynamically updated design document, and your reading and feedback is the driving force behind our progress, [GitHub Feedback Address](https://github.com/ant-design/ant-design/issues).

## Framework Information

![Structure diagram](https://gw.alipayobjects.com/zos/rmsportal/NyWYOFQxJYElAwtUfSdv.png)

The complete design pattern will include examples of templates, components (ETC), and general-purpose concepts:

- **Function example:** Consists of multiple templates to inspire users how to use and build a common feature.
- **Template:** A page-level example that inspires users how to build a typical page in a system, such as a detail page.
- **Component**
  - Basic components: The most basic elements of the system, such as buttons and pagers.
  - Business components/modules: Block-level examples, typically consisting of multiple components.
- **General concepts:** Some conventions that guarantee ETC systematization, such as typesetting, fonts, and copywriting.

## Resources

We work with engineers to transform design patterns into reusable code that maximizes your productivity and communication efficiency.

- [Ant Design Pro](https://pro.ant.design): Out-of-the-box solution with 20+ templates and 10+ business components.
- [Official UI](/components/overview): Ant Design's React UI library is a global component library with 60+ base components.
- [Axure Library](http://library.ant.design/): Axure resource packs are included with the code to make your prototype look like a visual draft, including templates, components, and more.
