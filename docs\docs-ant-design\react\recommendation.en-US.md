---
group:
  title: Other
  order: 3
order: 0
title: Third-Party Libraries
---

`antd` is built to implement [a set of high-quality React UI components](/components/overview) which follow Ant Design specification. It is impossible to include all useful components in one package, so we also recommend that using other great third-party libraries in React community.

| Category | Recommended Components |
| --- | --- |
| Visualization and charts | [Ant Design Charts](https://charts.ant.design) [AntV Data Visualization](https://antv.vision/en) |
| React Hooks Library | [ahooks](https://github.com/alibaba/hooks) |
| React Form Library | [ProForm](https://procomponents.ant.design/components/form) [Formily](https://github.com/alibaba/formily) [react-hook-form](https://github.com/react-hook-form/react-hook-form) [formik](https://github.com/formium/formik) |
| Router | [react-router](https://github.com/ReactTraining/react-router) |
| Layout | [react-grid-layout](https://github.com/react-grid-layout/react-grid-layout) [react-grid-system](https://github.com/sealninja/react-grid-system) [rc-dock](https://github.com/ticlo/rc-dock) |
| Drag and drop | [dnd-kit](https://github.com/clauderic/dnd-kit) [react-beautiful-dnd](https://github.com/atlassian/react-beautiful-dnd/) [react-dnd](https://github.com/gaearon/react-dnd) |
| Code Editor | [react-codemirror2](https://github.com/scniro/react-codemirror2) [react-monaco-editor](https://github.com/react-monaco-editor/react-monaco-editor) |
| Rich Text Editor | [react-quill](https://github.com/zenoamaro/react-quill) |
| JSON Viewer | [react-json-view](https://github.com/mac-s-g/react-json-view) |
| Color Picker | [react-colorful](https://github.com/omgovich/react-colorful) [react-color](https://casesandberg.github.io/react-color/) |
| Media Query | [react-responsive](https://github.com/contra/react-responsive) |
| Copy to clipboard | [react-copy-to-clipboard](https://github.com/nkbt/react-copy-to-clipboard) |
| Document head manager | [react-helmet-async](https://github.com/staylor/react-helmet-async) |
| Icons | [react-fontawesome](https://github.com/FortAwesome/react-fontawesome) [react-icons](https://github.com/gorangajic/react-icons) |
| QR Code | [qrcode.react](https://github.com/zpao/qrcode.react) |
| Top Progress Bar | [react-nprogress](https://github.com/tanem/react-nprogress) |
| i18n | [FormatJS](https://github.com/formatjs/formatjs) [react-i18next](https://react.i18next.com) |
| Code highlight | [react-syntax-highlighter](https://github.com/conorhastings/react-syntax-highlighter) |
| Markdown renderer | [react-markdown](https://remarkjs.github.io/react-markdown/) |
| Infinite Scroll | [rc-virtual-list](https://github.com/react-component/virtual-list/) [react-infinite-scroll-component](https://github.com/ankeetmaini/react-infinite-scroll-component) |
| Map | [google-map-react](https://github.com/istarkov/google-map-react) [@uiw/react-amap](https://github.com/uiwjs/react-amap) |
| Video | [react-player](https://github.com/CookPete/react-player) [video-react](https://github.com/video-react/video-react) [video.js](https://docs.videojs.com/tutorial-react.html) |
| Context Menu | [react-contexify](https://github.com/fkhadra/react-contexify) |
| Emoji | [emoji-picker-react](https://github.com/ealush/emoji-picker-react) [emoji-mart](https://github.com/missive/emoji-mart) |
| Split View | [react-split-pane](https://github.com/tomkp/react-split-pane) [react-resizable-panels](https://github.com/bvaughn/react-resizable-panels) |
| Image Crop | [antd-img-crop](https://github.com/nanxiaobei/antd-img-crop) [react-image-crop](https://github.com/DominicTobias/react-image-crop) |
| Keywords highlight | [react-highlight-words](https://github.com/bvaughn/react-highlight-words) |
| Text Loop | [react-text-loop-next](https://github.com/samarmohan/react-text-loop-next) [react-fast-marquee](https://github.com/justin-chu/react-fast-marquee) |
| Animation | [motion](https://github.com/framer/motion) [Ant Motion](https://motion.ant.design/components/tween-one) [react-spring](https://github.com/pmndrs/react-spring) |
| Page Footer | [rc-footer](https://github.com/react-component/footer) |
| Number/Currency | [react-countup](https://www.npmjs.com/package/react-countup) [react-number-format](https://github.com/s-yadav/react-number-format) [react-currency-input-field](https://github.com/cchanxzy/react-currency-input-field) |
| Application Frameworks | [umi](https://github.com/umijs/umi/) [remix](https://github.com/remix-run/remix) [refine](https://github.com/pankod/refine) |
| Flow-based UI | [reactflow](https://reactflow.dev/) [x6](https://github.com/antvis/x6) |
| Phone Input | [react-phone-number-input](https://gitlab.com/catamphetamine/react-phone-number-input) [antd-phone-input](https://github.com/ArtyomVancyan/antd-phone-input/) |
| AI Chat | [Ant Design X](https://github.com/ant-design/x) |
| PDF | [react-pdf](https://github.com/diegomura/react-pdf) [@react-pdf/renderer](https://github.com/diegomura/react-pdf) |
| React Gesture | [use-gesture](https://use-gesture.netlify.app) |

## Products we are using ✨

There are some products to recommend for developer/designer/product manager.

| Category          | Recommended Products                                                  |
| ----------------- | --------------------------------------------------------------------- |
| Documentation     | [🐦 Yuque](https://www.yuque.com/?chInfo=ch_antd)                     |
| Icon              | [Iconfont](https://www.iconfont.cn/)                                  |
| Sketch plugin     | [Kitchen](https://kitchen.alipay.com)                                 |
| Online Playground | [codesandbox](https://codesandbox.io/) [codepen](https://codepen.io/) |
| Image Compressor  | [tinypng](https://tinypng.com/)                                       |

<style>
.markdown table td:first-child {
  width: 20%;
  font-weight: 500;
}
.markdown table td > a:not(:last-child) {
  margin-inline-end: 18px;
}
.markdown table td > a:not(:last-child)::after {
  position: absolute;
  margin: 0 6px 0 8px;
  color: #bbb;
  content: '|';
  pointer-events: none;
}
</style>
