---
group: 全局样式
order: 5
title: 图标
---

图标是 UI 设计中必不可少的组成。通常我们理解图标设计的含义，是将某个概念转换成清晰易读的图形，从而降低用户的理解成本，提升界面的美观度。在我们的企业级应用设计范围中，图标在界面设计的诸多元素中往往只占了很小的比重，在调用时也会被缩到比设计稿小很多倍的尺寸，加上在图形素材极度丰富并且便于获取的今天，在产品设计体系中实现一套美观、一致、易用、便于延展的图标体系往往会被不小心忽略掉。Ant Design 相信一整套优质的图标对于设计质量的影响是非常巨大的，这考验着设计师的协作能力，以及对图形塑造的系统性思维，同时也能反映一个团队对于细节的追求。

Ant Design 在「确定」和「自然」的设计价值观影响之下，对全套的基础系统图标进行了改造，现在大家可以在我们网站上直接查看并使用新版的图标，同时在这里将我们的系统性思路分享给大家，抛砖引玉，期待有更多的设计师参与到这个寂寞的微观世界中来，一起把图形设计这件事做好。

[查看图标库](/components/icon-cn)

---

## 设计师专属

安装 [Kitchen Sketch 插件 💎](https://kitchen.alipay.com)，可以一键拖拽使用 Ant Design 和 Iconfont 的海量图标，还可以关联自有项目。

## 设计原则

Ant Design 的图标设计原则源自「确定」和「自然」，落实到图标设计领域，一共有四个，他们分别为：

- **准确：** 设计造型准确的图标（保持偶数原则，去小数点）；选择表意准确的图标，不对用户的认知造成困扰。
- **简单：** 在表意清晰准确的基础上，尽量保持图形的简洁，不做多余的修饰。
- **节奏：** 挖掘构图中的秩序之美。
- **愉悦：** 赋予适度的情感。

## 设计规格

- **Artboard：** Ant Design 的系统图标都是按照 1024 x 1024 的画板进行制作的：

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/mrrFTiCWOyCsVOgAIBqg.png" alt="设计规格">
</div>

- **出血位：** 在图标的设计过程中预留出血位的做法，可以预防某些造型的图标在具体应用时出现边缘被切掉的风险；同时在设计过程中，也为设计师把握图标间平衡留下了进退的余地。新版的设计规格在图形的外围预留了 64px 的出血位，多数的图标在设计中我们都不建议超过这个区域。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/FNXMpWnyvYfydiSnPCYg.png" alt="出血位">
</div>

## 分层

Ant Design 的图标设计对于设计稿的分层也有一定的要求，其目的除了让设计师实现有序的文档管理之外，更多的是便于团队间文档的传递，统一的设计框架像是无形的共识，可以让彼此间的理解得到进一步的提升。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/bVtUZqDRbGuaoVbwYqua.png" alt="分层">
</div>

## 轮廓线与模版

我们对设计模版进行了优化，根据出血位的尺寸，调整轮廓线的宽高，同时增加两个等边三角形和一个圆，这些都是图标设计中最常用的基本形式，设计师可以快速的调用并在此基础上做变形。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/ycDkLxfAqjnRsWZuHvik.png" alt="轮廓线与模版">
</div>

## 图标设计指引

根据「确定性」和「自然」的价值观，当构图含义明确之后，图标设计所追求的便是秩序之美。Ant Design 的图标主要通过四方面去实现「秩序美」，分别是：形式、韵律、平衡以及辨识。

### 1、形式

形式，是构成一个图形最初始的结构。Ant Design 整套基础图标基本上都是由圆、方、三角这样的图形演变而成的。追求图形初始结构的理性，而非直觉式的设计，是秩序之美的第一步。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/HpESYoDACMTUWLEqtBRb.png" alt="形式">
</div>

### 2、韵律

Ant Design 图标的韵律感通过两个方面来体现：元素的韵律和构图的韵律。系统图标中最常见的元素基本上可以归纳称为：点、线、圆角、三角。

#### 2-1. 元素的韵律

- **点：** 点是很多图形中都会出现的元素。Ant Design 会在一套图标中挖掘同一元素的规律，同时对其进行克制的运用。我们对于点的尺寸选择上会保持 16 的倍数这一原则。比如，在点的选择中，新版的图标最常用的是四种尺寸的点，分别为 80、96、112、128。当出现特殊尺寸的需求时，会按照 16 的倍数进行延展。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/qOHVVNiWAiQoHRjkuntb.png" alt="点">
</div>

- **线：** 线条也是非常通用的元素之一。新版图标在线条之间的关系采用 8 倍数原则，从小到大以 8 的规律递增。常用的规格也是 4 种，分别为 56、64、72、80。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/WqaPFOssksoRsfSEMYgc.png" alt="线">
</div>

- **圆角：** 圆角的规格采取的也是 8 倍数原则，最常用的是 3 种，分别是 8，16、32，它们之间是两倍数的关系。而图标内部空间的圆角则保持直角的处理方式。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/EycXTskdagLPlYMTvfdC.png" alt="圆角">
</div>

- **三角：** 新版图标的角度受到美式战斗机 F-14 Tomcat 的启发，将常用的角度定在约 76 度。在日常设计中，多数系统图标的角度都可以从 76 度这个数值出发，根据实际情况进行灵活的应用。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/WWnwBEQKIOhIeqbsIHZe.png" alt="三角">
</div>

除了定义角度，我们对新图标中实心箭头的规格也做了收敛，在顶角大约保持 76 度的基础上，最常用的为 4 种，他们的宽度保持 8 倍数的原则，间隔为 24 ：

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/jOZvfCdFTfpFxSkJwiMF.png" alt="实心箭头">
</div>

基本元素在使用上的规格可以用下面的表格来总结，建议设计师在一套图标的设计中尽量保持克制的态度。

| 点  | 线  | 圆角 | 三角 |
| --- | --- | ---- | ---- |
| ... | ... | /    | ...  |
| 80  | 56  | 8    | 144  |
| 96  | 64  | 16   | 216  |
| 112 | 72  | 32   | 240  |
| 128 | 80  | ...  | 264  |
| ... | ... |      | ...  |

#### 2-2、构图的韵律

在图标体系中，除了对重复出现的元素进行管理之外，我们还建议通盘的去考虑设计构图上的节奏感。

- 保持类似图标在构造上的一致性也是建立图标体系节奏感的一种方法。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/jpsoohmwvVgwSsblgPbc.png" alt="节奏感">
</div>

- 此外，在单个图标的设计过程中，也建议适当理性的看待各元素间的比例关系，而非直觉式的开展绘制。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/XorcLWyrefyAmYagUpgY.png" alt="元素间的比例关系">
</div>

### 3、平衡

要保持整套图标在视觉重量上的平衡，是一件不太容易的事并且是一件需要大量实践的工作。图标的造型、线条摆放的角度甚至是留白空间等，都是会影响视觉平衡的因素，因此需要设计师适时的通过对基本元素规格上的微调来达到图标的平衡感。

- **弯曲的线条会比竖直的线条看起来细**。 因此在圆形的外边框上我们会适当的对 72px 的规格进行 4 px 的微调。

  <div>
    <img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/GsrZFQbjQXwQoDRMnhKX.png" alt="弯曲的线条会比竖直的线条看起来细">
  </div>

- **倾斜的线条也会比竖直的线条看起来细**。 因此倾斜的线条也会进行 4 px 的微调。

  <div>
    <img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/wfRqkxabWTKdQgiVSzKh.png" alt="倾斜的线条也会比竖直的线条看起来细">
  </div>

- **图形的留白空间也是值得推敲的课题**。 当某些图形的留白不足时，可以通过调节线条的粗细来达到视觉重量上的平衡。

  <div>
    <img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/fVyyVdYqEXyjmxlWLtVw.png" alt="图形的留白空间也是值得推敲的课题">
  </div>

### 4、辨识

辨识度是一套图标具备的可被感知的特色，通常和系统本身的品牌基因相关。Ant Design 的系统图标在这一次除了遵循「确定」和「自然」这两块价值观，在辨识度这一块也做了两处小尝试。

- **让科技有温度：** 通过对于图形圆角的定义，将过于圆润的圆角（72）调整至（32），在视觉效果上令图标看起来更为坚硬和理性（对应科技感），但又不至于太过尖锐（有温度）。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/aPRvNTmHNYxBoOeijhTM.png" alt="让科技有温度">
</div>

- **让图形有生命：** 在部分图标设计中，会适度的注入拟人化的元素，令图标具备生命力。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/scJOuEdiwCgPONdiCZYZ.png" alt="让图形有生命">
</div>

## 给设计师的一些建议

在完成图标设计后，保持图形的整洁，图层结构的清晰，也是构筑图标体系必不可少的部分，Ant Design 对设计师有几点建议如下：

- 干掉多余的节点，保持图形的整洁。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/GTrZirSrsMIawXVjwoDI.png" alt="保持图形的整洁">
</div>

- 合并图形，便于输出。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/CIKruspXHoWevGWpoXJO.png" alt="合并图形">
</div>

- 对小数点以及奇数进行最后一遍的走查与修正。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/ySgIPVZBqPOWXOmTHTzT.png" alt="走查与修正">
</div>

- 整洁的图层管理。

<div>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/zxExIlRfcDTAowrkesHD.png" alt="图层管理">
</div>

## 写在最后

图标的设计是 UI 设计中非常容易被忽略的环节，建立优秀的图形体系也不是一两个设计人员的事，需要整个团队在设计前、设计中以及设计后都能够达成共识并且通力合作去完成共建。本次图标的升级，仅仅是一个开始。我们建议在调用图标时，考虑具体业务对于图形化寄予的期望，以及用户操作时的心智模型等因素，结合实际情况做调用和适当的二次设计。
