{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.11.1", "@types/node": "^24.0.13", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "prisma": "^6.11.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "nodemon": "^3.1.10"}}