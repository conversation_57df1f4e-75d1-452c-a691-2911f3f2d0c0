{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "echo \"Error: no test specified\" && exit 1", "create-test-user": "ts-node scripts/create-test-user.ts", "seed-users": "ts-node src/scripts/seed-users.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.11.1", "@types/node": "^24.0.13", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "jsonwebtoken": "^9.0.2", "prisma": "^6.11.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "zod": "^4.0.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "nodemon": "^3.1.10"}}