---
group: 设计模式
type: 原则
order: 9
title: 巧用过渡
---

人脑灰质（Gray Matter）会对动态的事物（例如：移动、形变、色变等）保持敏感。在界面中，适当地加入一些过渡效果，能让界面保持生动，同时也能增强用户和界面的沟通。

- Adding: 新加入的信息元素应被告知如何使用，从页面转变的信息元素需被重新识别。
- Receding: 与当前页无关的信息元素应采用适当方式移除。
- Normal: 指那些从转场开始到结束都没有发生变化的信息元素。

---

## 在视图变化时保持上下文

<video class="transition-video-player" alt="滑入与滑出示例" src="https://os.alipayobjects.com/rmsportal/EejaUGsyExkXyXr.mp4"></video>

滑入与滑出：可以有效构建虚拟空间。

<br>

<video class="transition-video-player" alt="传送带示例" src="https://os.alipayobjects.com/rmsportal/GIutPgZMTyfFfrH.mp4"></video>

传送带：可极大地扩展虚拟空间。

<br>

<video class="transition-video-player" alt="折叠窗口示例" src="https://os.alipayobjects.com/rmsportal/ERKhqHlcHiCDSQu.mp4"></video>

折叠窗口：在视图切换时，有助于保持上下文，同时也能拓展虚拟空间。

<br>

---

## 解释刚刚发生了什么

<video class="transition-video-player" alt="对象增加示例" description="新增一条对象时，该行「高亮」告知用户这是新增项；几秒后「高亮」消失，以免过度干扰用户。" src="https://os.alipayobjects.com/rmsportal/FqkQMyFqNqielOw.mp4"></video>

对象增加：在列表/表格中，新增了一个对象。

<br>

<video class="transition-video-player" alt="对象删除示例" src="https://os.alipayobjects.com/rmsportal/pnNkNIMoowmGUQy.mp4"></video>

对象删除：在列表/表格中，删除了一个对象。

<br>

<video class="transition-video-player" alt="对象更改示例" description="状态一：用户更改了「详情」中的值；<br>状态二：用户点击「保存」后，详情所在的网格出现「黄底」，表明该对象发生了更改；<br>状态三：底色持续几秒后，恢复正常。" src="https://os.alipayobjects.com/rmsportal/XrUIWmsmOlEnZGc.mp4"></video>

对象更改：在列表/表格中，更改了一个对象。

<br>

<video class="transition-video-player" alt="对象呼出示例" src="https://os.alipayobjects.com/rmsportal/gSNilqbiXOufDXF.mp4"></video>

对象呼出：点击页面中元素，呼出一个新对象。

---

## 改善感知性能

当无法有效提升「实际性能」时，可以考虑适当转移用户的注意力，来缩短某项操作的感知时间，改善感知性能。

<br>

---

## 自然运动

参见 [Ant Motion 动画语言](https://motion.ant.design/language/basic-cn)。
