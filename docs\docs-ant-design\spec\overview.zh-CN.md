---
group:
  title: 设计模式
  order: 2
order: 0
title: 概览
---

> 这是一份动态更新的设计文档，你的阅读和反馈是我们前进的动力。

在企业级业务中使用设计模式，可以显着增加研发团队的确定性，节省不必要的设计，保持系统一致性，让设计师专注于创意最需要的地方。

设计模式遵循 Ant Design 设计价值观，并为企业产品中反复出现的设计问题提供通用解决方案。设计者可以直接使用设计模式来完成界面设计，也可以以设计模式为出发点，衍生出更加针对业务的解决方案，以满足个性化的设计需求。

同时，这是一份动态更新的设计文档，您的阅读和反馈是我们前进的动力，[GitHub 反馈地址](https://github.com/ant-design/ant-design/issues)。

## 框架信息

![结构图](https://gw.alipayobjects.com/zos/rmsportal/NyWYOFQxJYElAwtUfSdv.png)

完整的设计模式将包括模板、组件 (ETC) 和通用概念的示例：

- 功能示例：由多个模板组成，以启发用户如何使用和构建一个通用功能。
- 模板：一个页面级示例，启发用户如何在系统中构建典型页面，例如详细信息页面。
- 组件
  - 基本组件：系统的最基本元素，例如按钮和分页器。
  - 业务组件/模块：块级示例，通常由多个组件组成。
- 一般概念：保证 ETC 系统化的一些约定，例如排版、字体和文案。

## 资源

我们与工程师合作，将设计模式转化为可重用的代码，最大限度地提高您的生产力和沟通效率。

- [Ant Design Pro](https://pro.ant.design/)：具有 20 多个模板和 10 多个业务组件的开箱即用解决方案。
- [React 官方实现](/components/overview-cn)：Ant Design 的 React UI 库拥有 60 多个基础组件。
- [Axure 设计库](http://library.ant.design/)：代码中包含 Axure 资源包，使您的原型看起来像一个视觉草稿，包括模板、组件等。
