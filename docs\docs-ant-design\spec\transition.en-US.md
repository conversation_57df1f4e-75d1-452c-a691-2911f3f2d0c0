---
group: Design Patterns
type: Principles
order: 9
title: Use Transition
---

Our Gray Matter are wired to react to dynamic things like movement, shape change and color change. Transitions smooth out the jarring world of the Web, making changes appear more natural. The main purpose for Transitions is to provide an engaging interface and reinforce communication.

- Adding: The added elements should inform the users how to use, and the modified elements should be recognized.
- Receding: The irrelevant page elements should be removed properly.
- Normal: The elements without any change on the page can be safely ignored.

---

## Maintain Context While Changing Views

<video class="transition-video-player" alt="example of Slide In and Slide Out
" src="https://os.alipayobjects.com/rmsportal/EejaUGsyExkXyXr.mp4"></video>

Slide In and Slide Out: Create an illusion of virtual space.

<br>

<video class="transition-video-player" alt="example of Carousel" src="https://os.alipayobjects.com/rmsportal/GIutPgZMTyfFfrH.mp4"></video>

Carousel: Carousels are great for extending virtual space.

<br>

<video class="transition-video-player" alt="example of Accordion" src="https://os.alipayobjects.com/rmsportal/ERKhqHlcHiCDSQu.mp4"></video>

Accordion: Accordion helps maintain context while switching views.

<br>

---

## Explain What Just Happened

<video class="transition-video-player" alt="example of Adding an Object" description="When an object is added, the highlighted area shows it to the user. The highlight fades in several seconds in order not to interfere the user flow." src="https://os.alipayobjects.com/rmsportal/FqkQMyFqNqielOw.mp4"></video>

Adding an Object: Add an object in the table or chart.

<br>

<video class="transition-video-player" alt="example of Deleting Objects" src="https://os.alipayobjects.com/rmsportal/pnNkNIMoowmGUQy.mp4"></video>

Deleting an Object: Delete an object in the table or chart.

<br>

<video class="transition-video-player" alt="example of Modifying an object" description="Status No.1: The user modifies the value of Detail. <br>Status No.2: After the user click the save button, a yellow fill is displayed in the grid of Detail, which indicates the change of the object. <br>Status No.3: The fill fades in several seconds and returned to normal." src="https://os.alipayobjects.com/rmsportal/XrUIWmsmOlEnZGc.mp4"></video>

Modifying an Object: Modify an object in the table or chart.

<br>

<video class="transition-video-player" alt="example of Calling out an Object" src="https://os.alipayobjects.com/rmsportal/gSNilqbiXOufDXF.mp4"></video>

Calling out an Object: Click the page element and call out a new object.

---

## Improve Perceived Performance

If actual performance can hardly improved, there is a difference between actual performance and perceived performance. Diverting the user's attention is a good way to improve the perceived time an operation takes.

---

## Natural Motion

Please refer to [Ant Motion, a motion language](https://motion.ant.design/language/basic).
