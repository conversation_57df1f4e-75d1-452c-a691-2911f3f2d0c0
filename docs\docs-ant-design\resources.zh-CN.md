---
order: 1
title: 资源
description: 这里汇总了与 Ant Design 相关的所有资源。
---

## 设计资源

这里提供 Ant Design 相关设计资源和设计工具的下载，更多设计资源正在整理和完善中。你可以在这个[地址](https://www.yuque.com/kitchen/topics/216)中反馈对新版本 Sketch Symbols 组件的意见。

<div class="next-block-use-cards"></div>

<ResourceCards>
- Sketch 组件包
  - https://gw.alipayobjects.com/zos/basement_prod/048ee28f-2c80-4d15-9aa3-4f5ddac50465.svg
  - 桌面组件 Sketch 模板包
  - https://github.com/ant-design/ant-design/releases/download/5.13.3/AntDesign5.0_UI.KIT_202401.sketch
  - 官方
- Mobile Components
  - https://gw.alipayobjects.com/zos/basement_prod/c0c3852c-d245-4330-886b-cb02ef49eb6d.svg
  - 移动组件 Sketch 模板
  - https://gw.alipayobjects.com/os/bmw-prod/d6266aef-25b7-4892-b275-ce214121831c.sketch
  - 官方
- Ant Design Pro
  - https://gw.alipayobjects.com/zos/basement_prod/5edc7f4d-3302-4710-963b-7b6c77ea8d06.svg
  - 典型页面 + 通用业务模板
  - https://gw.alipayobjects.com/os/bmw-prod/22208f9d-f8c5-4d7c-b87a-fec290e96527.sketch
  - 官方
- 全新 Chart 组件包
  - https://gw.alipayobjects.com/zos/basement_prod/a9dc586a-fe0a-4c7d-ab4f-f5ed779b963d.svg
  - 桌面组件 Chart 模板包
  - https://gw.alipayobjects.com/os/bmw-prod/704968a5-2641-484e-9f65-c2735b2c0287.sketch
  - 官方
- Kitchen
  - https://gw.alipayobjects.com/zos/basement_prod/d475d063-2754-4442-b9db-5d164e06acc9.svg
  - Sketch 工具集
  - http://kitchen.alipay.com
  - 官方
- Ant Design Landing
  - https://gw.alipayobjects.com/zos/basement_prod/b443f4be-5116-49b7-873f-a7c8502b8f0e.svg
  - 首页模板集
  - https://landing.ant.design/docs/download-cn
  - 官方
- Figma 组件包
  - https://gw.alipayobjects.com/zos/basement_prod/7b9ed3f2-6f05-4ddb-bac3-d55feb71e0ac.svg
  - 在 Figma 使用 Ant Design 进行设计
  - https://www.antforfigma.com
- Figma 开源组件包
  - https://gw.alipayobjects.com/zos/basement_prod/7b9ed3f2-6f05-4ddb-bac3-d55feb71e0ac.svg
  - 代码级精确度的免费开源 Figma 完全组件库
  - https://www.figma.com/community/file/831698976089873405
- 如意设计助手
  - https://github.com/ant-design/ant-design/assets/507615/45201521-37d0-4360-b81e-a1260dedad7a
  - Figma 插件，使用 antd 代码组件库进行设计，交付对开发者友好的组件代码
  - https://www.figma.com/community/plugin/1192146318523533547
- 墨刀设计资源包
  - https://cdn.modao.cc/logo_mockingbot.svg
  - 内置丰富的 Ant Design 组件资源
  - https://modao.cc/square/ant-design
- 即时设计资源包
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*dxzdQYWlmjMAAAAAAAAAAAAAARQnAQ
  - 可在「即时设计」在线免费使用的全套组件和模板
  - https://js.design/antd
- MasterGo 资源包
  - https://mastergo-local-default.oss-cn-beijing.aliyuncs.com/ant-design-mastergo.svg
  - 可在「MasterGo」在线免费使用的全套组件和模板
  - https://mastergo.com/community/?utm_source=antdesign&utm_medium=link&utm_campaign=resource&cata_name=AntDesign
- Raycast 拓展
  - https://gw.alipayobjects.com/zos/basement_prod/5edc7f4d-3302-4710-963b-7b6c77ea8d06.svg
  - mac 用户可使用 Raycast 快速打开 Ant Design 组件
  - https://www.raycast.com/crazyair/antd-open-browser
</ResourceCards>

## 文章

想要了解 Ant Design 设计体系背后的故事？如何才能更好的应用 Ant Design？你可以查阅下述我们为你精挑细选的文章。也欢迎关注 [Ant Design 官方专栏](https://www.zhihu.com/column/c_1310524851418480640)，这里常有关于 Ant Design 设计体系下相关话题内容的最新分享和讨论，如 Ant Design、AntV 可视化、Kitchen 设计插件、B 端产品设计、SaaS 产品设计、自然交互、增长设计、智能设计、设计工程化等。

<ResourceArticles></ResourceArticles>

## 致敬

在 Ant Design 4.0 的改版中，我们汲取顶级设计体系的精华，同时结合我们自身业务特性做了大量优化。我们希望通过不断努力和打磨，成为世界级设计体系的一份子，为「用户」和「设计者」带来极致体验。如果你也想追求卓越，建议去研究这些体系： [Fiori Design](https://experience.sap.com/fiori-design-web/)、 [Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/)、 [Lightning Design System](https://lightningdesignsystem.com/getting-started/)、 [Material Design](https://material.io/)

<div class="next-block-use-cards"></div>

<ResourceCards>
- About Face 4 #E1E8B7
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*KKZWRozT8D8AAAAAAAAAAABkARQnAQ
  - 一本数字产品和系统的交互设计指南
  - http://book.douban.com/subject/26642302/
- Web 界面设计 #009C94
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*yB0oQ42f0kEAAAAAAAAAAABkARQnAQ
  - Web 界面的最佳实践、模式和原理
  - http://book.douban.com/subject/3821157/
- 界面设计模式 #9489CF
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*aFAfTKIjR_IAAAAAAAAAAABkARQnAQ
  - 界面设计总体思路指引
  - http://book.douban.com/subject/25716088/
- 写给大家看的设计书 #AFBCC8
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*tTvXQYApsIIAAAAAAAAAAABkARQnAQ
  - 优秀设计所必须遵循的基本原则
  - http://book.douban.com/subject/3323633/
- 设计心理学 1 #B7D9B7
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*12W8R7nPxxUAAAAAAAAAAABkARQnAQ
  - 强调以人为本的设计哲学
  - http://book.douban.com/subject/26102860/
- 设计心理学 3 #EFBDB5
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*a5VNSamt2EIAAAAAAAAAAABkARQnAQ
  - 解释情感因素在设计领域扮演的角色
  - http://book.douban.com/subject/26424688/
- Web 表单设计 #C2DAED
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*vXfQS7sStNYAAAAAAAAAAABkARQnAQ
  - 表单设计的真谛
  - http://book.douban.com/subject/4886100/
</ResourceCards>

## 加入我们

蚂蚁集团 Ant Design 团队是一支兼具设计视角和工程视角的横向组织，服务蚂蚁集团上百个中后台系统，主打产品 Ant Design 服务全球 100 万设计师和工程师，是西湖区学院路西侧最具影响力的设计语言。欢迎来这里和我们一起打造优雅高效的人机设计/研发体系。

### UI/UE 设计师

简历和作品集请投递：jiayin.liu#antgroup.com

> 注明简历来自 ant.design 官网

- 岗位级别：P5/P6/P7/P8
- 岗位地点：杭州
- 岗位要求：
  - 至少 3-5 年的工作经验，扎实设计功底；
  - 抽象能力强，善于透过表象找本质；
  - 沟通能力佳，善于自我管理；
  - 有企业级设计实战经验，加分；
  - 有数据驱动的增长设计实践，加分；
  - 深度理解 SAP、Salesforce、Google 等设计体系，能提出自己独到见解并落实到实践中，加加加分。
- 岗位职责：
  - 参与[蚂蚁链](https://blockchain.antgroup.com/)、人工智能、数据平台等企业级产品的设计工作；
  - 参与[语雀](https://www.yuque.com/) 等创新产品的设计工作；
  - 参与 Ant Design 的打磨，将其建设成全球卓越的设计体系。
  - 参与 AntV 的打磨，将其建设成全球一流的数据可视化体系。
- One More Thing ❤️ ：
  - 你们总是为世界带去美好，但总是忘却你们也需要美好。我们正在努力打造 [🍳 Kitchen：一款为设计师提效的 Sketch 工具集](https://kitchen.alipay.com/)等专属设计师的产品，让设计真正变成财富。期待志同道合的你，一道给设计行业带来「微小而美好的改变」。

### 前端工程师

简历请投递：<EMAIL>

> 注明简历来自 ant.design 官网

- 岗位级别：P5/P6/P7/P8
- 岗位地点：杭州/上海
- 岗位要求：
  - 在 React 技术栈持续耕耘，情有独钟。
  - 热爱开源。
  - 坚持和善于用技术和工具解决其他问题。
  - 丰富的中后台前端研发经验。
- 岗位职责：
  - 负责 Ant Design 前端基础设施研发。
  - 负责中后台设计/前端工具体系建设。

### Node.js 工程师

简历请投递：<EMAIL>

> 注明简历来自 ant.design 官网

- 岗位级别：P5/P6/P7/P8
- 岗位地点：杭州/上海
- 岗位要求：
  - 在 Node.js 技术栈持续耕耘，情有独钟。
  - 热爱开源。
  - 坚持和善于用技术和工具解决其他问题。
  - 丰富的 Node.js 研发经验。
- 岗位职责：
  - 负责 Node.js 前端基础设施研发。
  - 负责大前端工具体系建设。

### ADI（Artificial Design Intelligence） 工程师

简历和作品集请投递：jiayin.liu#antgroup.com

> 注明简历来自 ant.design 官网

- 岗位级别：P7/P8
- 岗位地点：杭州
- 岗位要求：
  - 有良好的工程师背景，善于学习和使用各类工具、框架解决研发问题；
  - 对人工智能应用在设计行业，有坚定的信心和意愿；
  - 已经有相关实践工作，优先考虑。
- 岗位职责：
  - 负责 Ant Design 工具体系和智能设计的研发，并配合团队成员进行商业化实践，把设计做成业务；
  - 组建和培养有梯度的研发团队。
