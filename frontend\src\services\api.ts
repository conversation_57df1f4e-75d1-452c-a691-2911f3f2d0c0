import axios from 'axios';
import { message } from 'antd';

// API base URL
const API_BASE_URL = 'http://localhost:3001/api';

// Axios instance oluştur
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor - token ekle
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor - hata yönetimi
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          message.error('Yetkisiz erişim. Lütfen tekrar giriş yapın.');
          localStorage.removeItem('auth_token');
          break;
        case 403:
          message.error('<PERSON>u işlem için yetkiniz bulunmuyor.');
          break;
        case 404:
          message.error('İstenen kaynak bulunamadı.');
          break;
        case 500:
          message.error('Sunucu hatası. Lütfen daha sonra tekrar deneyin.');
          break;
        default:
          message.error(data?.message || 'Bir hata oluştu.');
      }
    } else if (error.request) {
      // Network error
      message.error('Sunucuya bağlanılamıyor. İnternet bağlantınızı kontrol edin.');
    } else {
      // Other error
      message.error('Beklenmeyen bir hata oluştu.');
    }
    
    return Promise.reject(error);
  }
);

// API functions
export const authAPI = {
  // PIN ile giriş - backend username bekliyor
  pinLogin: async (username: string, pin: string) => {
    const response = await api.post('/auth/pin-login', { username, pin });
    return response.data;
  },

  // Kullanıcı listesi (vardiya başlatabilecek kullanıcılar)
  getUsers: async () => {
    const response = await api.get('/auth/users');
    return response.data;
  },

  // Çıkış
  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },
};

export default api;
