---
group: Ant Design
order: 1
title: 设计价值观
---

Ant Design 设计价值观为设计者提供评价设计好坏的内在标准，启示并激发了 Ant Design 设计原则和设计模式，进而为具体设计问题提供向导和一般解决方案。

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*8mRLT7esgZYAAAAAAAAAAABkARQnAQ" alt="总概" />
</div>

在「设计价值观」的坚持上，Ant Design 有四点与众不同：

## 自然

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*zx7LTI_ECSAAAAAAAAAAAABkARQnAQ" alt="自然" />
</div>

数字世界的光速迭代使得产品日益复杂，而人类意识和注意力资源有限。面对这种设计矛盾，追求「自然」交互将是 Ant Design 持之以恒的方向。

- **感知自然**：认知心理学所述，约 80% 外界信息通过视觉通道获取。界面设计中最重要的视觉要素，包括布局、色彩、插画、图标等，应充分汲取自然界规律，从而降低用户认知成本，带来真实流畅的感受。在一些场景下，适时加入听觉、触觉等其它感知通道，能创造更丰富自然的产品体验。
- **行为自然**：在与系统的互动中，设计者应充分理解用户、系统角色、任务目标间的关系，场景化组织系统功能和服务。同时辅以行为分析、人工智能、传感器、元数据等策略，提供主动式服务，帮助用户决策、减少操作，从而节约用户脑力和体力，让人机交互行为更自然。

> 想了解自然价值观的前世今生，[请移步至专栏](https://zhuanlan.zhihu.com/p/44809866)。

## 确定性

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*yHjSQKAhF5kAAAAAAAAAAABkARQnAQ" alt="确定性"/>
</div>

界面是用户与系统交互的媒介，是手段而非目的。在追求「自然」交互基础上，通过 Ant Design 创造的产品界面应是高确定性、低合作熵的状态。

- **设计者确定**：企业级产品都是分工合作的产物，参与者越多合作熵越高，这是一切设计工作低效、产品系统不易维护的来源。通过探索设计规律、模块化设计思路，来为设计者提供足够精简的设计规则、组件、模式等，赋能设计者、降低合作熵。
  - 保持克制： 能做，但想清楚了不做。设计者应当聚焦在最有价值产品功能打磨，并用尽可能少的设计元素将其表达。正如 Antoine de Saint-Exupéry 所说：完美不在于无以复加，而在于无可删减，万事莫不如此。
  - 面向对象的方法： 探索设计规律，并将其抽象成「对象」，增强界面设计的灵活性和可维护性，同时也减少「设计者」的主观干扰，从而降低系统的不确定性。例如：色值换算、间距排版。
  - 模块化设计： 将复杂或者重复出现的局部封装成模块，提供有限接口与其他模块互动，最终全面减少系统的复杂度，进而增进可靠性以及可维护性。设计者可运用现有的组件/模板或者自行抽象可复用的组件/模板，节约无谓的设计且保持系统一致性，让「设计者」把创造力专注在最需要的地方。
- **用户确定**：用户日常工作是通过诸多企业级产品的协同来完成的，除了考虑单一产品的设计一致性，更应当在跨产品、跨终端、跨系统间保持良好的确定性。一致的外观和交互，保持面向用户的熟悉感，能提升易学性，降低认知和操作成本，提升工作效率。

## 意义感

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*xOYlR4e8ihIAAAAAAAAAAABkARQnAQ" alt="意义感" />
</div>

一个产品或功能被设计者创造出来不只是出于用户的需要，而更多地是承载用户的某个工作使命。产品设计应充分站在工作视角，促成用户使命的达成；同时，在「自然」、「确定」之上，兼顾用户的人性需求，为工作过程创造富有意义感的人机交互。

- **结果的意义：明确目标，即时反馈**。洞悉工作目标，根据使用流程拆解明确的子目标，让每个交互行为都围绕着主目标的达成；为每个行为，辅以恰当、即时的反馈，让用户对操作结果了然于胸。此外，可通过情感化设计，适度安抚用户负面情感，强化用户正面情感。
- **过程的意义：挑战适中，全情投入**。调整不同场景下的工作难度，让功能适时适地触发，以匹配用户能力；如无必要，勿增实体，不分散用户注意力，让用户专注于任务达成，而非界面。让当下的工作既不过于简单，亦不过于复杂，挑战适中，并随着用户能力的成长提出更高的挑战，能让用户持续沉浸在工作的心流中，获得富有成就感的工作体验。

## 生长性

<div>
  <img src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*pKz3TabovrEAAAAAAAAAAABkARQnAQ" alt="Growth" />
</div>

企业级产品功能的增长与用户系统角色的演变相生相伴。设计者应为自己创造的产品负责，提升功能、价值的可发现性。用发展的眼光做设计，充分考虑人、机两端的共同生长。

- **价值连接**：产品的增长依赖于用户的群体扩大和深度使用，而用户的成长又依赖于产品功能的完善。设计者应建立系统设计思维，洞悉产品功能的价值，探索用户在不同场景下的需求，在价值和需求间建立连接。让产品价值被发现，帮助用户建立更有效、更高效的工作方式。
- **人机共生**：产品功能和用户需求的更多连接，让人机互动更加紧密，用户和系统共生。在进行产品设计时，不应将用户和系统独立开来，而应将两者作为一个动态发展的共同体来思考，确保其足够的灵活、包容，充满生命力。
