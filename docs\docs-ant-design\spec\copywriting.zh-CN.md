---
group: 设计模式
type: 全局规则
order: 5
title: 文案
---

在界面中，我们需要通过对话的方式与用户产生共鸣。精准、清晰的语言会更容易让用户理解，合适的语气更容易让用户建立信任感。因此在界面设计时，文案也应当被重视。 在使用和书写文案时有以下几点需要注意：

- 从用户角度出发
- 表述一致
- 重要的信息放在显著位置
- 专业、精准、完整
- 精简、友好、正面

---

## 语言

在界面中，文案是我们与用户沟通的基础，语言文字的表述也需要精心推敲，仔细设计。清晰、准确、简洁的文案设计能够让界面拥有更好的可用性，同时让用户体验更加友好。

### 明确表述立足点

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/uBzzoUAMupDWPXFUeRIn.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/EOVTgwoOsYptbkUqpMKn.png" alt="错误示范" description="侧重在「我们」为用户提供了什么，而不是以用户视角的关注点为中心。">
</ImagePreview>

在表述内容时，关注点应该是用户和他们能用你的产品做什么，而不是你和你的产品在为他们做什么，所以内容表述的立足点很重要。

既然以用户为中心，文案就应该尽量以用户为主体来写作。

> 注：当用户向后台反馈问题、提出建议或申诉时，使用「我们」是合理的语境，例如「我们将会审核你的申诉」。

### 精简语句

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/dAcEgVUcAcUqbMjaEydw.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/xRUVqOQsBOqzdmZIQDLa.png" alt="错误示范">
</ImagePreview>

省略无用词汇，不重复用户已知事实；在绝大多数交互场景下，都无需界面描述出全部的细节。

尽量提供简短、易于快速获取的内容。

### 使用用户熟悉的语言

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/FOcLNnbiaZOTMRHAyeVZ.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/tUmMsssHDlGqlKbRwYlH.png" alt="错误示范" description="站在用户的角度，说用户熟悉的话。">
</ImagePreview>

使用简单、直接、易于理解的词汇，让内容和指示更容易被用户接受和理解。

间接、暧昧模糊的说法，生僻和过于「文雅」的用词，会增加用户的认知负荷，所以应当尽量避免使用这类用户无法识别的词汇。

### 表述一致

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/ToMaEybHQCrcAfcYRbxF.png" alt="正确示范" description="备注描述使用相同的介词。">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/BUFFQwfvLqTLrOzaEccX.png" alt="错误示范" description="同一区块描述中出现了「受」和「被」两个不同的介词。">
</ImagePreview>

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/AAaXSGqNUBlZChkrMbYl.png" alt="正确示范" description="在同一页面、同一区域在语序上一致。">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/DYCoXTphnpdkMMCtyMdN.png" alt="错误示范" description="语序上不一致，会影响用户理解成本。">
</ImagePreview>

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/sNqQYWEJCAzCxcYCBGYD.png" alt="操作名称和目标页面的标题一致。">
</ImagePreview>

- 描述同一个事物的词汇要保持统一；
- 上下文的语法、语种、语序要保持统一；
- 操作的名称和目标页面标题的名称保持一致。

### 重要的信息放在显著位置

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/clWcgMqBypLAAosLQHes.png" alt="正确示范" description="在有限的空间内将重要的信息放在最前面（或通过高亮、留白等方式突出重要信息）。">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/hSiLeNGgmeqWvVHCNoeE.png" alt="错误示范" description="用户最关心的信息内容藏在段落中，不易搜寻。">
</ImagePreview>

让用户第一眼看到最重要的内容，不用到段落中寻找。

> 注：如考虑安全性问题时，隐私信息也可调整为「点击后可见」的方式。

### 完整、直接地阐述信息

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/ioBKvBqCNzUwQDyjMiIa.png" alt="正确示范" description="用户可以从中了解了设置后会有什么好处。">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/EiwnPMETQAmWlHSGAWEX.png" alt="错误示范" description="用户感受不到设置的意义，不会去设置。">
</ImagePreview>

当我们希望用户进行一个操作时，要专注于用户能得到什么，以及用户的感受。在操作前引导告知用户操作的目的或重要性，能促进用户更愿意去执行。

<br />

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/dlAkFzezQEwtNnZDWpQh.png" alt="正确示范" description="相对于「失败」，「无法完成」是更加客观的结果，更容易让用户在心理上接受。用户需要知道在出现问题的情况下如何进行下一步操作。">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/qqrgyclPnhBFgPEYsBXd.png" alt="错误示范" description="对于异常情况不是冷冰冰告诉你「失败」。">
</ImagePreview>

报错是 UI 中常见的功能，它同样是用户体验中不可小视的组成部分。当用户填写的内容出错的时候，你的报错信息应当符合用户的认知，用易于理解的方式表述出来。

### 用词精准完整

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/mCusyeTfzbyDCYxvwEPM.png" alt="正确示范" description="完整的表达。">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/cugctFUEXcVUNnsZRLMD.png" alt="错误示范" description="不完整，有歧义或太口语化。">
</ImagePreview>

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/jvYzIkRbdICzNvxeVfBr.png" alt="正确示范" description="专业用语精准。">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/TFhFTbqHlEXTMOyCxIvN.png" alt="错误示范" description="专业的行业用词有别字。">
</ImagePreview>

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/qIwLlXOyJQlAqFwWSUuo.png" alt="正确示范" description="时间信息的表述精准完整。">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/HBCyJcXZYmtLECZUtjzE.png" alt="错误示范" description="时间信息的描述词不是具体的「日」、「月」，容易让用户产生困扰。">
</ImagePreview>

<ImagePreview className="markdown" pure="true">
<table style="font-size:12px;">
  <tr>
    <th style="border-bottom: 2px solid #108ee9;width:20%;">使用</th>
    <th style="border-bottom: 2px solid #f04134;width:25%;">不使用</th>
    <th>备注</th>
  </tr>
  <tr>
    <th>其他</th>
    <td>其它</td>
    <td>「其他」的应用范围更广</td>
  </tr>
  <tr>
    <th>抱歉</th>
    <td>对不起</td>
    <td>如果是我们系统造成的结果，可以使用「抱歉」，如果是用户自己造成的结果，不能使用。</td>
  </tr>
  <tr>
    <th>验证码</th>
    <td></td>
    <td>4位或多位数字或字母图片，可有效防止黑客发起对账户的登录尝试。</td>
  </tr>
  <tr>
    <th>校验码</th>
    <td></td>
    <td>手机或即时通讯工具收到的6位数字，用于验证用户的身份。</td>
  </tr>
  <tr>
    <th>登录</th>
    <td>登陆</td>
    <td>登记记录用户输入的注册账号和密码。</td>
  </tr>
  <tr>
    <th>此</th>
    <td>该</td>
    <td>当要表达当前事物时，「此」更加明确。</td>
  </tr>
</table>
</ImagePreview>

通用基本用词要规范，不要写错字，词语表达要完整。

专业用语要精准，并是所属行业认可通用用词；时间的表述必须明确。

---

## 语气

语言定义的是内容，而情绪和气氛更多地是通过语气来表达，并且同样的内容面对不同的用户我们可以使用不同的语气来表达；例如，我们对应专业的运维人员和小白用户应有不同的表达方式。

### 拉近彼此的距离

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/LXVkAEabvRXwOTYkewzV.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/KxSWWhUiCPYaadFucIEr.png" alt="错误示范" description="建议不要使用「您」，太过客气，让用户感觉有些疏远。">
</ImagePreview>

直接使用「你」、「我」来和用户对话，与用户建立亲密感。避免使用「您」，让用户感觉太过疏远。

<br />

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/mkFlHfRJxVaFpCVnhJxz.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/SwzkNWboQRPncuORBPmL.png" alt="错误示范" description="同时出现了称谓「我」和「你」，用户会感到迷惑，不清楚到底指代对象是谁。">
</ImagePreview>

> 注：不要在同一个句式中混用「你」和「我」，交互中指代混乱会让用户相当纠结。

### 友好、尊重用户

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/SiyDiAnuljqDrZgcFiXn.png" alt="正确示范" description="引导用户正确输入内容。">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/ZSgEJWJJeOYBDDsenOuS.png" alt="错误示范" description="「不能」、「不要」、「请勿」都给人命令或强迫的感觉。">
</ImagePreview>

多给用户支持与鼓励，不要命令和强迫用户。

如果你想留住你的用户，当出错的时候就不要责怪用户。专注于解决问题，而不是指责。

### 表述不应过于极端

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/zXLrYMCesvdZXdSoJEcP.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/MTIsErHJIswPHVfUFQnI.png" alt="错误示范" description="「绝不」过于绝对，让用户感到不适。">
</ImagePreview>

不要使用过于绝对的表述，这样会让用户觉得不适。

## 大小写和标点符号

### 英文名词大小写规范

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/ruuIBHvkqfJrCNuWrGZZ.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/yEqZfFLnyUACJhprXCqt.png" alt="错误示范">
</ImagePreview>

产品名称全称，首字母大写。产品名称缩写需要全部大写，如：ESC、SLB 等；

> 注：整个单词都大写不利于阅读和识别，应尽量避免这种用法。

<br />

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/aVOygdsFMYeQZJNWDouv.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/obNsZSLYKAqMtsXrEHYg.png" alt="错误示范">
</ImagePreview>

正确使用专有名词的大小写规范。

<br />

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/sDSMymVeCJGoMJLBpoHe.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/tUQvbqMLPGFjwiywBmJP.png" alt="错误示范">
</ImagePreview>

全英文的标题，标签，菜单项等等都要遵循英文句式中首字母大写的规范。

### 统计数据使用阿拉伯数字

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/WOtjvzMQnfuAHJXcifgW.png" alt="正确示范" description="阿拉伯数字的信息传递效率更高">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/LhJSOSsQrMFCxtFHqNqL.png" alt="错误示范">
</ImagePreview>

这也是常见问题，用户对于数字的感知速度更快，使用数字而非文字表述会更加有效。

### 省略不必要的标点

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/QGpLpUFgZnTDzYJCeuun.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/fQhiFpjLcHJtJJGzElUT.png" alt="错误示范">
</ImagePreview>

为了帮助用户更加高效地扫视文本内容，可以省略不必要的断句点。

以下元素单独出现时可以省略标点：

- 标签
- 标题
- 输入框下的提示
- 悬停文本中的提示
- 表格中的句子

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/UMLpWSOrmsYFlozQFGXu.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/raqemTlQncDdOczUCKvo.png" alt="错误示范">
</ImagePreview>

以下元素单独出现时需要加上标点：

- 多句或多段的文案和列表内容。
- 任何文字链前的句子。

### 谨慎使用感叹号

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/CJAEXjDelaghIOHZAxgh.png" alt="正确示范">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/SgcrhDeaVpNmeFWRiJnc.png" alt="错误示范">
</ImagePreview>

感叹号会让文案显得过于激动，容易让气氛变得过于紧张。

> 注：当向用户表达问候或祝贺时，使用「！」是合理的语境，例如「欢迎回到社区！」。

### 基本标点规范

<ImagePreview className="markdown" pure="true">
<table style="font-size:12px;">
  <tr>
    <th>标点名称</th>
    <th>字符</th>
    <th>描述</th>
  </tr>
  <tr>
    <th>空格</th>
    <td> </td>
    <td>段落句子中的链接和文字之间增加空格；
全角字符和半角字符搭配时，需要添加空格，如：两个、2 个、50%。</td>
  </tr>
  <tr>
    <th>句号</th>
    <td>。</td>
    <td>以下情况中不使用句号：输入框下的提示；表格中的句子；句末为文字链（链接前使用句号）；按钮和标题。</td>
  </tr>
  <tr>
    <th>感叹号</th>
    <td>！</td>
    <td>只在需要表达强烈情感的情况下使用。</td>
  </tr>
  <tr>
    <th>连接号</th>
    <td>-</td>
    <td>不使用中文全角的连接号。如：2012-11-12。</td>
  </tr>
  <tr>
    <th>省略号</th>
    <td>…</td>
    <td>使用半角的「…」为省略号。</td>
  </tr>
  <tr>
    <th>隐藏符号</th>
    <td>*</td>
    <td>多用于替换显示隐私信息。</td>
  </tr>
</table>
</ImagePreview>

正确地使用标点符号会让句子看起来更清晰和具有可读性。

具体使用请参考 1995 年中国标准出版社出版的[《标点符号用法》](http://www.moe.gov.cn/ewebeditor/uploadfile/2015/01/13/20150113091548267.pdf)，右图为重点列出的在设计中需要注意的部分。
