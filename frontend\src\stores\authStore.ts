import { create } from 'zustand';

export interface User {
  id: string;
  username: string;
  fullName: string;
  role: string;
  avatar?: string;
  shiftStart?: string;
  shiftEnd?: string;
}

interface AuthState {
  // Kullanıcı listesi
  users: User[];
  selectedUser: User | null;
  
  // PIN giriş durumu
  pin: string;
  isLoading: boolean;
  error: string | null;
  
  // Auth durumu
  isAuthenticated: boolean;
  currentUser: User | null;
  token: string | null;
  
  // Actions
  setUsers: (users: User[]) => void;
  setSelectedUser: (user: User | null) => void;
  setPin: (pin: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  login: (token: string, user: User) => void;
  logout: () => void;
  clearPin: () => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  // Initial state
  users: [],
  selectedUser: null,
  pin: '',
  isLoading: false,
  error: null,
  isAuthenticated: false,
  currentUser: null,
  token: null,
  
  // Actions
  setUsers: (users) => set({ users }),
  setSelectedUser: (user) => set({ selectedUser: user, error: null }),
  setPin: (pin) => set({ pin }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  login: (token, user) => set({ 
    token, 
    currentUser: user, 
    isAuthenticated: true, 
    isLoading: false,
    error: null 
  }),
  logout: () => set({ 
    token: null, 
    currentUser: null, 
    isAuthenticated: false,
    selectedUser: null,
    pin: '',
    error: null
  }),
  clearPin: () => set({ pin: '', error: null }),
}));
