Basit bir restoran POS projesi başlatıyorum.

Stack:
- Frontend: Electron + React + TypeScript + Ant Design
- Backend: Express + TypeScript + Prisma + PostgreSQL

İlk adım olarak:
1. Frontend ve backend klasörlerini ayır
2. Her ikisi için TypeScript config
3. Backend'de Express + Prisma setup
4. Frontend'de Electron + React setup
5. Basit bir health-check API ve frontend'de test
6. PostgreSQL veritabanı kurulumu
7. Prisma migration'ları yap

Karmaşık yapılar kullanma, minimal ve çalışan kod ver.