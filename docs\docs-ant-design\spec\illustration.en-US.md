---
order: 5
title: Illustrations
---

<div style="text-align:center;">
  <img alt="General" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*WzMpTIP8R6gAAAAAAAAAAABkARQnAQ" />
</div>

## Background information

Illustrations are a key component of a brand's recognition. It is prevalent in both digital products and offline goods. Contrasting from Copyrighted information, illustrations not only carry information through intuition but also carries emotions and resulting in higher immersion and empathy for the customer. This leads to better user experience while accomplishing business goals. Illustrations contains inherit complexity due to the apparent personal style of the artist. It is a challenge to reproduce a consistent style between a team of artists, on the other hand, there is inherit risk when relying on a single artist for all the illustrations in a project. An illustration system is particular crucial in providing consistent branding. improving productivity. Avoiding risk in this regard is of great importance.

<div style="text-align:center;">
  <img alt="Background" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*rSUBTL8hv9sAAAAAAAAAAABkARQnAQ" />
</div>

## Design Principles

From the most foundational design principles to the uppermost design techniques, HiTu adopted the design inspired by ETCG. Illustrations are grouped. Usable as templates when divided and entire use flows when congregated. This supplements the design with great flexibility and customization ability.

<div style="text-align:center;">
  <img alt="Design principle" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*WKEzS5-_zYAAAAAAAAAAAABkARQnAQ" />
</div>

#### HiTu Pyramid Model

We concretized the 4 abstract cornerstones that make up our design strategy; Technology, Certainty, Nature, Growth. Technology represents a strong engineering foundation, providing strong support for all digital products while opening a window of possibility. As the diagram shows, we can visualize the relationships between personality and products, between past experiences and the future. The combinations between each of them could meet the demands of many different business requirements.

<div style="text-align:center;">
  <img alt="Pyramid model" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*gCoSS5DaCNEAAAAAAAAAAABkARQnAQ" />
</div>

## Colors

### Sea Hare Swatch

<ImagePreview>
<img class="preview-img no-padding" description="Sea Hare's color palette" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*5ZE6RrjW-jQAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

Sea Hare's color matching system is inspired by Ant Design's application of color palette in scenes. Differing from the UI's color scheme. The color matching system used in illustrations will be relatively more vibrant and flexible. Taking inspiration from Ant Design's basic color palette, we tweaked brightness and tolerance. The result is more efficient and easy to use. Since it originates from Any Design's color palette, it integrates seamlessly with other UI assets.

### Default Asset Colors of Sea Hare

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*0Dv9Rrp7GtMAAAAAAAAAAAAAARQnAQ" />
</ImagePreview>

Through research, we discovered blue and white accounts for a large proportion among enterprise products. We chose Geek Blue as our primary color for its technological, exploration and focused vibes.

<br />

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*U5L-RKWlmJcAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

Sea Hare's palette combined with Adobe's ternary color picker and mosaic ball, you can easily obtain the default version of the basic color palette.

<br />

# Design Assets

### Illustrations of People

<ImagePreview>
<img class="preview-img" alt="Natural head-to-body ratio" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*rm9JRIqTmPgAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

In view of the natural design principle, we do not recommend using Q version cartoons and overly exaggerated artistic styles. Rather, we recommend a realistic head-to-body ratio.

<br />

<ImagePreview>
<img class="preview-img" alt="9 variants of common professional character design" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*Zt7BSI2OL7gAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

Concurrently, we integrated emotions when designing the 9 common professional roles. Fusing some characteristics of the role while radiating vastly different personalities, meeting the needs of varies business requirements.

<br />

<ImagePreview>
<img class="preview-img" alt="Breakdown of each character" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*jUujRJBxU-sAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="HiTu skeletal system" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*C3NCT6QHU9QAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="Reusing different postures" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*NZuwQp_vcIQAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

Taking the basic character design, we break down each character and rearrange them to match the desired skeleton structure. This means various postures can be reused and extended.

<br />

### Elementary Components

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*ph0YSZmq-ekAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" alt="Elementary components designed for business settings" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*Z8oxS5ym3PIAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

Memory comes from difference and professionalism from uniformity. Elementary Components refers to some status in the business settings that are constantly shifting and changing. We hope to achieve uniformity while not constraining creativity. To achieve a consistent sense of rhythm, we recommend a 1024\*1024 grid while maintaining a rounded corner with sizes that are multiples of 8.

<br />

# Usage

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*puHVQJEe-oIAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*W-PzSadIFeAAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*OJoaQ77tczIAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

<ImagePreview>
<img class="preview-img" src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*IySSSoBaGPYAAAAAAAAAAABkARQnAQ" />
</ImagePreview>

How do I utilize this wealth of assets? With HiTu's design principles as a guide, I recommend designers to construct a sense of spatial awareness along the Z-axis, dividing the illustration into 3 layers of foreground, middle ground and background. Placing the key elements in the foreground (such as people, elementary components, etc), environment and context in the middle and creating atmosphere in the background. The foreground should also have the highest saturation and visibility, both decreasing in level as the level decreases.

<br />
