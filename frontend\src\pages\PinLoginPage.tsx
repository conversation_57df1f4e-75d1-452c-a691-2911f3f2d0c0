import React, { useEffect } from 'react';
import { Layout, Typography, <PERSON>ton, Spin, message } from 'antd';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '../components/ui/input-otp';
import NumericKeypad from '../components/NumericKeypad';
import UserSelector from '../components/UserSelector';
import { useAuthStore } from '../stores/authStore';
import { authAPI } from '../services/api';

const { Content } = Layout;
const { Title, Text } = Typography;

// Mock data - gerçek uygulamada API'den gelecek
const mockUsers = [
  {
    id: '1',
    username: 'intan.fauziah',
    fullName: 'Intan Fauziah',
    role: 'CASHIER',
    avatar: '/api/placeholder/40/40',
    shiftStart: '16:00',
    shiftEnd: '00:00',
  },
  {
    id: '2',
    username: 'ahmad.kusuma',
    fullName: '<PERSON>',
    role: '<PERSON>IT<PERSON>',
    avatar: '/api/placeholder/40/40',
    shiftStart: '08:00',
    shiftEnd: '16:00',
  },
  {
    id: '3',
    username: 'nasih.stevens',
    fullName: 'Nasih Stevens',
    role: 'BRANCH_MANAGER',
    avatar: '/api/placeholder/40/40',
    shiftStart: '12:00',
    shiftEnd: '20:00',
  },
  {
    id: '4',
    username: 'elsa.peters',
    fullName: 'Elsa Peters',
    role: 'CASHIER',
    avatar: '/api/placeholder/40/40',
    shiftStart: '06:00',
    shiftEnd: '14:00',
  },
];

const PinLoginPage: React.FC = () => {
  const {
    users,
    selectedUser,
    pin,
    isLoading,
    error,
    setUsers,
    setSelectedUser,
    setPin,
    setLoading,
    setError,
    login,
    clearPin,
  } = useAuthStore();

  // Kullanıcıları yükle
  useEffect(() => {
    const loadUsers = async () => {
      try {
        setLoading(true);
        // Gerçek API çağrısı
        const response = await authAPI.getUsers();
        setUsers(response.data || mockUsers);
      } catch (error) {
        console.error('API hatası, mock data kullanılıyor:', error);
        // API hatası durumunda mock data kullan
        setUsers(mockUsers);
        message.warning('Kullanıcılar yüklenirken sorun oluştu, demo veriler kullanılıyor.');
      } finally {
        setLoading(false);
      }
    };

    loadUsers();
  }, [setUsers, setLoading]);

  // PIN girişi
  const handleNumberClick = (number: string) => {
    if (pin.length < 6) {
      setPin(pin + number);
    }
  };

  // Backspace
  const handleBackspace = () => {
    setPin(pin.slice(0, -1));
  };

  // PIN değişikliği
  const handlePinChange = (value: string) => {
    if (value.length <= 6 && /^\d*$/.test(value)) {
      setPin(value);
    }
  };

  // Vardiya başlat
  const handleStartShift = async () => {
    if (!selectedUser) {
      message.error('Lütfen bir kullanıcı seçin.');
      return;
    }

    if (pin.length !== 6) {
      message.error('PIN 6 haneli olmalıdır.');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      // API çağrısı - backend username bekliyor
      const response = await authAPI.pinLogin(selectedUser.username, pin);

      // Başarılı giriş - backend response.data içinde token ve user var
      if (response.success && response.data) {
        const { token, user } = response.data;

        // User objesini frontend formatına çevir
        const frontendUser = {
          id: user.id,
          username: user.username,
          fullName: `${user.firstName} ${user.lastName}`,
          role: user.role,
        };

        login(token, frontendUser);
        message.success('Vardiya başarıyla başlatıldı!');

        // Token'ı localStorage'a kaydet
        localStorage.setItem('auth_token', token);
      } else {
        throw new Error(response.message || 'Giriş başarısız');
      }
      
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'PIN hatalı. Lütfen tekrar deneyin.';
      setError(errorMessage);
      message.error(errorMessage);
      clearPin();
    } finally {
      setLoading(false);
    }
  };

  // PIN tamam mı kontrol et
  const isPinComplete = pin.length === 6;
  const canStartShift = selectedUser && isPinComplete && !isLoading;

  return (
    <Layout className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Content className="flex items-center justify-center p-4">
        <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          
          {/* Sol taraf - Arka plan görseli ve slogan */}
          <div className="hidden lg:block">
            <div className="bg-white rounded-2xl shadow-xl p-8 h-full flex flex-col justify-center">
              <div className="text-center mb-8">
                <Title level={2} className="text-gray-800 mb-4">
                  Aleo.
                </Title>
                <Text className="text-gray-600 text-lg">
                  CAFE & RESTO
                </Text>
              </div>
              
              <div className="bg-gray-100 rounded-xl p-6 mb-6">
                <div className="text-center">
                  <Text className="text-gray-700 text-lg italic">
                    "Müşterilere temiz, rahat bir restoranda en iyi tadı sunan yemekleri 
                    uygun fiyatlarla sunun, böylece tekrar gelmeye devam edecekler."
                  </Text>
                  <div className="mt-4">
                    <Text className="text-gray-600 font-medium">
                      Dave Thomas, Wendy's Kurucusu
                    </Text>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sağ taraf - Giriş formu */}
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <Title level={2} className="text-gray-800 mb-2">
                Aleo.
              </Title>
              <Text className="text-gray-600 mb-6">CAFE & RESTO</Text>
              
              <Title level={3} className="text-blue-600 mb-4">
                Çalışan Girişi
              </Title>
            </div>

            <div className="space-y-8">
              {/* Kullanıcı Seçimi */}
              <UserSelector
                users={users}
                selectedUser={selectedUser}
                onUserSelect={setSelectedUser}
                loading={isLoading && users.length === 0}
              />

              {/* PIN Giriş Alanı */}
              {selectedUser && (
                <>
                  <div className="text-center">
                    <Text className="text-gray-600 mb-4 block">
                      Kimliğinizi doğrulamak için PIN'inizi girin.
                    </Text>
                    
                    <div className="flex justify-center mb-6">
                      <InputOTP
                        maxLength={6}
                        value={pin}
                        onChange={handlePinChange}
                        disabled={isLoading}
                      >
                        <InputOTPGroup>
                          {[0, 1, 2, 3, 4, 5].map((index) => (
                            <InputOTPSlot 
                              key={index} 
                              index={index}
                              className="w-12 h-12 text-lg border-gray-300 focus:border-blue-500"
                            />
                          ))}
                        </InputOTPGroup>
                      </InputOTP>
                    </div>
                  </div>

                  {/* Sayısal Tuş Takımı */}
                  <NumericKeypad
                    onNumberClick={handleNumberClick}
                    onBackspace={handleBackspace}
                    disabled={isLoading}
                  />

                  {/* Vardiya Başlat Butonu */}
                  <Button
                    type="primary"
                    size="large"
                    block
                    onClick={handleStartShift}
                    disabled={!canStartShift}
                    loading={isLoading}
                    className="h-12 text-lg font-semibold"
                  >
                    {isLoading ? 'Kontrol Ediliyor...' : 'Vardiya Başlat'}
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default PinLoginPage;
