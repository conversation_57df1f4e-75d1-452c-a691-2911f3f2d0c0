---
order: 1
title: Resources
description: List all the resources that are related with Ant Design here.
---

## Design Resources

Please find below some of the design resources and tools about Ant Design that we consider valuable. More of this is still being collected. You can leave feedback about Sketch Symbols [here](https://www.yuque.com/kitchen/topics/216).

<div class="next-block-use-cards"></div>

<ResourceCards>
- Sketch Symbols
  - https://gw.alipayobjects.com/zos/basement_prod/048ee28f-2c80-4d15-9aa3-4f5ddac50465.svg
  - Sketch Symbols for Desktop
  - https://github.com/ant-design/ant-design/releases/download/5.13.3/AntDesign5.0_UI.KIT_202401.sketch
  - Official
- Mobile Components
  - https://gw.alipayobjects.com/zos/basement_prod/c0c3852c-d245-4330-886b-cb02ef49eb6d.svg
  - Sketch Symbols File for Mobile
  - https://gw.alipayobjects.com/os/bmw-prod/d6266aef-25b7-4892-b275-ce214121831c.sketch
  - Official
- Ant Design Pro
  - https://gw.alipayobjects.com/zos/basement_prod/5edc7f4d-3302-4710-963b-7b6c77ea8d06.svg
  - Common Templates and Pages
  - https://gw.alipayobjects.com/os/bmw-prod/22208f9d-f8c5-4d7c-b87a-fec290e96527.sketch
  - Official
- Ant Design Chart
  - https://gw.alipayobjects.com/zos/basement_prod/a9dc586a-fe0a-4c7d-ab4f-f5ed779b963d.svg
  - Sketch Symbols for Chart
  - https://gw.alipayobjects.com/os/bmw-prod/704968a5-2641-484e-9f65-c2735b2c0287.sketch
  - Official
- Kitchen
  - https://gw.alipayobjects.com/zos/basement_prod/d475d063-2754-4442-b9db-5d164e06acc9.svg
  - A Sketch plugin collection
  - http://kitchen.alipay.com
  - Official
- Ant Design Landing
  - https://gw.alipayobjects.com/zos/basement_prod/b443f4be-5116-49b7-873f-a7c8502b8f0e.svg
  - Landing Templates
  - https://landing.ant.design/docs/download-cn
  - Official
- Figma Resources
  - https://gw.alipayobjects.com/zos/basement_prod/7b9ed3f2-6f05-4ddb-bac3-d55feb71e0ac.svg
  - Always up-to-date Ant Design Figma resources
  - https://www.antforfigma.com
- Figma Open Source Library
  - https://gw.alipayobjects.com/zos/basement_prod/7b9ed3f2-6f05-4ddb-bac3-d55feb71e0ac.svg
  - Free open source Figma library with complete accurate to code components
  - https://www.figma.com/community/file/831698976089873405
- AntBlocks UI for Figma
  - https://uploads-ssl.webflow.com/64dc925e7cb893427a5c9cdc/64e4610f7818dcc7501057ad_antblocks-ui-card-img.svg
  - High-quality, responsive, and customizable React components built on Ant Design
  - https://www.antblocksui.com/#figma
- Ruyi Design Assistant
  - https://github.com/ant-design/ant-design/assets/507615/45201521-37d0-4360-b81e-a1260dedad7a
  - Figma Plugin,Design using Antd code component library and deliver component code that is friendly to developers
  - https://www.figma.com/community/plugin/1192146318523533547
- UI Kit for Adobe XD
  - https://uploads-ssl.webflow.com/5ecbd337fe499992c9ed75ba/5f2a7a30f3e817085cec5ac9_ant-xd-svg.svg
  - Library of components for Desktop
  - https://www.antforxd.com
- MockingBot
  - https://cdn.modao.cc/logo_mockingbot.svg
  - Rich component resources
  - https://modao.cc/square/ant-design
- JiShi Design
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*dxzdQYWlmjMAAAAAAAAAAAAAARQnAQ
  - Use full components and templates from JiShi Design
  - https://js.design/antd
- MasterGo
  - https://mastergo-local-default.oss-cn-beijing.aliyuncs.com/ant-design-mastergo.svg
  - Use full components and templates from MasterGo
  - https://mastergo.com/community/?utm_source=antdesign&utm_medium=link&utm_campaign=resource&cata_name=AntDesign
- Ant for Plasmic
  - https://user-images.githubusercontent.com/7129/149994038-76214796-cd6a-4e80-b0a4-117e8edac050.png
  - Drag/drop live Ant components and manipulate props in this React visual builder
  - https://www.plasmic.app/ant-design
</ResourceCards>

## Articles

Do you want to know the story behind the Ant Design design system? How can I better apply Ant Design? You can check out our well selected articles below. You are also welcome to follow [Ant Design Official Column](https://www.zhihu.com/column/c_1310524851418480640). There you will find the latest sharings and discussions related to the Ant Design design system, such as Ant Design, AntV visualization, Kitchen design Plug-ins, B-side product design, SaaS product design, natural interaction, growth design, intelligent design, design engineering, etc.

<ResourceArticles></ResourceArticles>

## Reference

Please find below the books that inspired us, saved our time and helped us to overcome difficulties when designing components and patterns. If you want to know more about UI design, we recommend you these awesome design systems: [Fiori Design](https://experience.sap.com/fiori-design-web/), [Human Interface Guidelines](https://developer.apple.com/ios/human-interface-guidelines/), [Lightning Design System](https://lightningdesignsystem.com/getting-started/), [Material Design](https://material.io/).

<div class="next-block-use-cards"></div>

<ResourceCards>
- About Face 4 #C7EBD6
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*GA-CRIRqKjgAAAAAAAAAAABkARQnAQ
  - The Interactive Design Guide for Digital Products and System
  - https://www.wiley.com/en-sg/About+Face%3A+The+Essentials+of+Interaction+Design%2C+4th+Edition-p-9781118766576
- Designing Web Interfaces #009C94
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*KK2xSJu0M80AAAAAAAAAAABkARQnAQ
  - Best Practice, Patterns and Principles for Web Interface
  - http://shop.oreilly.com/product/9780596516253.do
- Designing Interfaces #9489CF
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*slN2QpTvIs0AAAAAAAAAAABkARQnAQ
  - Interface Design Guidelines
  - https://www.amazon.com/Designing-Interfaces-Patterns-Effective-Interaction/dp/1449379702/ref=pd_sbs_14_t_1/131-2623973-6077764?_encoding=UTF8&pd_rd_i=1449379702&pd_rd_r=ebe12a8d-435f-474b-a593-72aadf26c45a&pd_rd_w=18rob&pd_rd_wg=bhRFl&pf_rd_p=5cfcfe89-300f-47d2-b1ad-a4e27203a02a&pf_rd_r=8V8CD0EE336ZZEG15DEN&psc=1&refRID=8V8CD0EE336ZZEG15DEN
- Non-Designer's Design Book, The, 4th Edition #FAF0CD
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*1HbNSIju7pEAAAAAAAAAAABkARQnAQ
  - Basic Principles of Good Design
  - http://www.peachpit.com/store/non-designers-design-book-9780133966152
- The Design of Everyday Things #F8F3D1
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*4woBSLvOjfMAAAAAAAAAAABkARQnAQ
  - About the People-oriented Design Philosophy
  - https://jnd.org/the-design-of-everyday-things-revised-and-expanded-edition/
- Emotional Design #E8EEB4
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*6ZQJQoKRORsAAAAAAAAAAABkARQnAQ
  - Explain the Role of Emotional Factors in Design
  - https://www.amazon.com/Emotional-Design-Love-Everyday-Things/dp/0465051367
- Web Form Design #C2DAED
  - https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*VhhwRo7axKQAAAAAAAAAAABkARQnAQ
  - The Essence of Form Design
  - https://rosenfeldmedia.com/books/web-form-design/
</ResourceCards>
