---
group: Global Styles
order: 5
title: Icons
---

An icon is a graphical representation of meaning. Icons can be used to express actions, state, and even to categorize data. Ant Design's icons adhere to the following two principles and are designed for cross-platform consistency:

- Graphics that are clear, intuitive, and simple enjoy a higher degree of recognition and are more easily understood
- All icons in the user interface should be consistent in style (detail design, perspective, stroke weight, etc.)

---

## System Icons

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/XzOPonWCsPjvgkrklCzo.png" alt="System Icons">
</ImagePreview>

System icons are often used to represent commonly used operations, such as: save, edit, delete. Ant Design also includes icons to represent file types and state.

- [View the icons](/components/icon/)

### Key Contour Lines

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/beTZeZjJBVuhMyTOhebs.png" alt="Ant Design's grid and key contour lines" alt="Key Contour Lines">
</ImagePreview>

Contour lines play an important role in making various icons with the same visual effect.

Please make all icons in the 1024×1024 resolution (16×16 64 times).

- [Illustrator tips](https://zos.alipayobjects.com/rmsportal/hmNuLjCkBssupcZgYAde.png)

<ImagePreview>
<img class="preview-img no-padding inline" src="https://gw.alipayobjects.com/zos/rmsportal/rxuHAKGEGLuqBJAAhnSm.png" alt="Square contour">
</ImagePreview>

<ImagePreview>
<img class="preview-img no-padding inline" src="https://gw.alipayobjects.com/zos/rmsportal/fhkmysGZiTkPVszWHgUy.png" alt="Horizontal rectangle contour">
</ImagePreview>

<ImagePreview>
<img class="preview-img no-padding inline" src="https://gw.alipayobjects.com/zos/rmsportal/BiraoJgbXokyzmUFqVuf.png" alt="Circular contour">
</ImagePreview>

<ImagePreview>
<img class="preview-img no-padding inline" src="https://gw.alipayobjects.com/zos/rmsportal/wgQLwFnPaeEalmmSuBMO.png" alt="Vertical rectangle contour">
</ImagePreview>

### Stroke Weight

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/uoNmxXiqKpfoFDdEVjUB.png" alt="Correct example" description="Line thickness is consistently 72px">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/cnFYbWzcKaPFSiHcptCF.png" alt="Incorrect example" description="Line thickness is not uniform">
</ImagePreview>

Consistent stroke weight is the key to maintaining the visual unity of the entire icon system. Ant Design's icons have a consistent line width of 72px.

### Corners

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/tGbuhPmvEJXmOFoYAkPK.png" alt="Correct example" description="The icon's corners are properly rounded">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/JekevTlqdDJQLIQwrppm.png" alt="Incorrect example" description="The icon's corners are not rounded">
</ImagePreview>

Consistent rounding of corners and sizing of angles is also an important element in maintaining visual unity.

Icons that follow Ant Design should have rounded corners and edges using a 72px radius.

### Visual Correction

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/VghPLyDUdFmjhGJlNxjV.png" alt="Correct example" description="For the intricate “JPG” lettering, an outline is added for readability">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/qnzloxRQmvHzHAhWWwCS.png" alt="Incorrect example" description="The text in the icon is crowded and hard to read">
</ImagePreview>

In certain special cases (for example, when the icon is too compact), adjustments to line width, outlines, or other subtle changes may be made to increase readability.

### Perspective

<ImagePreview>
<img class="preview-img no-padding good" src="https://gw.alipayobjects.com/zos/rmsportal/tIePnIOTXtgzVKbqwucm.png" alt="Correct example" description="Maintains a flat, simple style">
<img class="preview-img no-padding bad" src="https://gw.alipayobjects.com/zos/rmsportal/ALNOEelXeFhxUobLqyCq.png" alt="Incorrect example" description="Icons should not have depth nor varying perspectives">
</ImagePreview>

Always keep a simple, flat style. Icons should not have a sense of depth nor a large amount of detail.

### Naming Conventions

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/NFOvbdbVWeeEqOkdUfVB.png" alt="Naming Conventions">
</ImagePreview>

Uniform naming conventions make finding icons faster and easier. For example, icons with a surrounding outline have a uniform "-o" suffix.

### Icon Sizing

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/jAuedlyhNIDyOIZTqbqN.png" alt="Icon Sizing">
</ImagePreview>

Icons should be scaled according to the text size, according to the Ant Design specification.

For example, icons inline with 12pt font should be 12px in size with 8px of spacing.

### Color

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/LxGLhtnwvCqZWYqGDAAr.png" description="Colors demonstrated - @Black = #000000, @White = #FFFFFF, @Blue-6 = #1890FF" alt="Color">
</ImagePreview>

The color of the icon should be consistent the color of the surrounding copy, unless the icon is being used to express state (in which case it should be colored accordingly).

---

## Business Icons

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/EADAnRecKSTxvpxPzKoq.png" alt="Business Icons">
</ImagePreview>

Business icons, unlike system icons, do not themselves have functional operations, but rather an abstraction that assists with copywriting. Compared to the system icon, the business icon is more rich in the details of the design, the size of the use of relatively large.

> Note: Business icons design principles and system icons are basically the same, the details of the processing (such as stroke weight, fillet size, etc.) depending on the specific scene may be.

### Icon Sizing

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/uwAgfciGszhdiVlMSBXK.png" alt="Icon Sizing">
</ImagePreview>

In normal use, there are 32px (minimum size), 48px and 64px (maximum size) three options.

### Color

<ImagePreview>
<img class="preview-img no-padding" src="https://gw.alipayobjects.com/zos/rmsportal/wUxhTxZlHoTxDvneWBWO.png" alt="Color">
</ImagePreview>

There are two kinds of business icon, single-color (neutral color) and double-color (neutral color + primary color), the area of primary color does not exceed 40% of the entire icon.
