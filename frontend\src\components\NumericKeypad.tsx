import React from 'react';
import { Button } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

interface NumericKeypadProps {
  onNumberClick: (number: string) => void;
  onBackspace: () => void;
  disabled?: boolean;
}

const NumericKeypad: React.FC<NumericKeypadProps> = ({
  onNumberClick,
  onBackspace,
  disabled = false,
}) => {
  const numbers = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['', '0', 'backspace'],
  ];

  return (
    <div className="grid grid-cols-3 gap-4 w-full max-w-xs mx-auto">
      {numbers.map((row, rowIndex) =>
        row.map((item, colIndex) => {
          if (item === '') {
            return <div key={`${rowIndex}-${colIndex}`} />; // Bo<PERSON> alan
          }
          
          if (item === 'backspace') {
            return (
              <Button
                key={`${rowIndex}-${colIndex}`}
                size="large"
                onClick={onBackspace}
                disabled={disabled}
                className="h-16 text-xl font-semibold flex items-center justify-center"
                icon={<DeleteOutlined />}
              />
            );
          }
          
          return (
            <Button
              key={`${rowIndex}-${colIndex}`}
              size="large"
              onClick={() => onNumberClick(item)}
              disabled={disabled}
              className="h-16 text-xl font-semibold"
            >
              {item}
            </Button>
          );
        })
      )}
    </div>
  );
};

export default NumericKeypad;
